package com.ensar.forge;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
public class ProjectFilterTest {

    @Test
    public void testFilterParsing() {
        // This test verifies that the filter parsing logic works correctly
        // The actual filtering will be tested through the service layer
        
        String priorityFilter = "LOW,HIGH";
        String budgetFilter = "Under $10K,$10K - $25K,Over $100K";
        String locationFilter = "New York,San Francisco";
        String deadlineFilter = "2025-12-31,2025-01-01";
        
        // Test priority parsing
        String[] priorities = priorityFilter.split(",");
        assert priorities.length == 2;
        assert "LOW".equals(priorities[0].trim());
        assert "HIGH".equals(priorities[1].trim());
        
        // Test budget range parsing
        String[] budgetRanges = budgetFilter.split(",");
        assert budgetRanges.length == 3;
        assert "Under $10K".equals(budgetRanges[0].trim());
        assert "$10K - $25K".equals(budgetRanges[1].trim());
        assert "Over $100K".equals(budgetRanges[2].trim());
        
        // Test location parsing
        String[] locations = locationFilter.split(",");
        assert locations.length == 2;
        assert "New York".equals(locations[0].trim());
        assert "San Francisco".equals(locations[1].trim());
        
        // Test deadline parsing
        String[] deadlines = deadlineFilter.split(",");
        assert deadlines.length == 2;
        assert "2025-12-31".equals(deadlines[0].trim());
        assert "2025-01-01".equals(deadlines[1].trim());
    }
    
    @Test
    public void testBudgetRangeParsing() {
        // Test various budget range formats
        String[] testRanges = {
            "Under $10K",
            "$10K - $25K", 
            "Over $100K",
            "$50K+",
            "$25K"
        };
        
        // Verify that budget ranges can be split correctly
        for (String range : testRanges) {
            assert range != null && !range.trim().isEmpty();
            // Test that ranges contain expected characters
            if (range.contains("Under")) {
                assert range.contains("$") && range.contains("K");
            } else if (range.contains(" - ")) {
                assert range.contains("$") && range.contains("K");
            } else if (range.contains("Over")) {
                assert range.contains("$") && range.contains("K");
            } else if (range.contains("+")) {
                assert range.contains("$") && range.contains("K");
            }
        }
    }
}

