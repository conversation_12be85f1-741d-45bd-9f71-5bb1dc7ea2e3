package com.ensar.forge.controller;

import com.ensar.forge.dto.ProposalAttachmentRequest;
import com.ensar.forge.dto.ProposalAttachmentResponse;
import com.ensar.forge.service.ProposalAttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/proposal-attachments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Proposal Attachment Management", description = "APIs for managing proposal attachments")
public class ProposalAttachmentController {

    private final ProposalAttachmentService proposalAttachmentService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(
        summary = "Upload a proposal attachment",
        description = "Uploads a file attachment for a specific proposal"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Attachment uploaded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or file"),
        @ApiResponse(responseCode = "404", description = "Proposal not found"),
        @ApiResponse(responseCode = "409", description = "File with same name already exists")
    })
    public ResponseEntity<ProposalAttachmentResponse> uploadAttachment(
            @Parameter(description = "File to upload", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @RequestParam("proposalId") String proposalId) {
        
        ProposalAttachmentRequest request = new ProposalAttachmentRequest();
        request.setFile(file);
        request.setProposalId(proposalId);
        
        ProposalAttachmentResponse response = proposalAttachmentService.createAttachment(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get proposal attachment by ID",
        description = "Retrieves a specific proposal attachment with its metadata"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attachment retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Attachment not found")
    })
    public ResponseEntity<ProposalAttachmentResponse> getAttachment(
            @Parameter(description = "Attachment ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProposalAttachmentResponse response = proposalAttachmentService.getAttachment(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}/download")
    @Operation(
        summary = "Download proposal attachment",
        description = "Downloads the file content of a specific proposal attachment"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "File downloaded successfully",
                content = @Content(mediaType = "application/octet-stream")),
        @ApiResponse(responseCode = "404", description = "Attachment not found")
    })
    public ResponseEntity<byte[]> downloadAttachment(
            @Parameter(description = "Attachment ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        
        ProposalAttachmentResponse attachment = proposalAttachmentService.getAttachment(id);
        byte[] fileData = proposalAttachmentService.getAttachmentData(id);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(attachment.getFileType()));
        headers.setContentDispositionFormData("attachment", attachment.getFileName());
        headers.setContentLength(fileData.length);
        
        return new ResponseEntity<>(fileData, headers, HttpStatus.OK);
    }

    @GetMapping("/proposal/{proposalId}")
    @Operation(
        summary = "Get attachments by proposal",
        description = "Retrieves all attachments for a specific proposal"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attachments retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<List<ProposalAttachmentResponse>> getAttachmentsByProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String proposalId) {
        List<ProposalAttachmentResponse> attachments = proposalAttachmentService.getAttachmentsByProposal(proposalId);
        return ResponseEntity.ok(attachments);
    }

    @GetMapping("/proposal/{proposalId}/count")
    @Operation(
        summary = "Get attachment count by proposal",
        description = "Retrieves the number of attachments for a specific proposal"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attachment count retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<Long> getAttachmentCountByProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String proposalId) {
        Long count = proposalAttachmentService.getAttachmentCountByProposal(proposalId);
        return ResponseEntity.ok(count);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a proposal attachment",
        description = "Permanently deletes a proposal attachment"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Attachment deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Attachment not found")
    })
    public ResponseEntity<Void> deleteAttachment(
            @Parameter(description = "Attachment ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        proposalAttachmentService.deleteAttachment(id);
        return ResponseEntity.noContent().build();
    }
}
