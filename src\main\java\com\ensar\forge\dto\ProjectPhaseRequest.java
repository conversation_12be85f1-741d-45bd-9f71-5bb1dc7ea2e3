package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "Request object for creating or updating a project phase")
public class ProjectPhaseRequest {
    @Schema(description = "Phase id")
    private String id;

    @Schema(description = "Phase name", example = "Planning & Design", required = true)
    @NotBlank(message = "Phase name is required")
    @Size(max = 255, message = "Phase name cannot exceed 255 characters")
    private String name;

    @Schema(description = "Duration in weeks", example = "4")
    @Min(value = 1, message = "Duration must be at least 1 week")
    private Integer durationWeeks;

    @Schema(description = "Cost for this phase", example = "15000.00")
    @DecimalMin(value = "0.0", message = "Cost cannot be negative")
    private BigDecimal cost;
}