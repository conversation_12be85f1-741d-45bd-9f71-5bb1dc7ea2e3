package com.ensar.forge.repository;

import com.ensar.forge.entity.ProjectPhase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectPhaseRepository extends JpaRepository<ProjectPhase, String> {
    
    List<ProjectPhase> findByProposalId(String proposalId);
    
    @Query("SELECT pp FROM ProjectPhase pp WHERE pp.proposal.id = :proposalId ORDER BY pp.createdDateTime")
    List<ProjectPhase> findByProposalIdOrderByCreatedDateTime(@Param("proposalId") String proposalId);
    
    void deleteByProposalId(String proposalId);

    boolean existsByProposalIdAndName(String proposalId, String name);
}
