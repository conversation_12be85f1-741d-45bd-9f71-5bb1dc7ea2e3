package com.ensar.forge.repository;

import com.ensar.forge.entity.Skill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SkillRepository extends JpaRepository<Skill, String> {
    
    Optional<Skill> findByName(String name);
    
    List<Skill> findByNameContainingIgnoreCase(String name);
    
    boolean existsByNameIgnoreCase(String name);
}


