package com.ensar.forge.repository;

import com.ensar.forge.entity.UserSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserSkillRepository extends JpaRepository<UserSkill, String> {
    
    List<UserSkill> findByUserId(String userId);
    
    List<UserSkill> findBySkillId(String skillId);
    
    Optional<UserSkill> findByUserIdAndSkillId(String userId, String skillId);
    
    @Query("SELECT us FROM UserSkill us JOIN FETCH us.skill WHERE us.user.id = :userId")
    List<UserSkill> findByUserIdWithSkill(@Param("userId") String userId);
    
    void deleteByUserIdAndSkillId(String userId, String skillId);
    
    boolean existsByUserIdAndSkillId(String userId, String skillId);
}
