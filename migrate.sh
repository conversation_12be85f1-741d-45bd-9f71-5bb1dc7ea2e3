#!/bin/bash

echo "Forge Database Migration Tool"
echo "============================="
echo

if [ $# -eq 0 ]; then
    echo "Usage: ./migrate.sh [command]"
    echo
    echo "Commands:"
    echo "  status    - Show migration status"
    echo "  migrate   - Run pending migrations"
    echo "  info      - Show detailed migration info"
    echo "  repair    - Repair migration metadata"
    echo "  clean     - Clean database (WARNING: Drops all tables!)"
    echo
    echo "Examples:"
    echo "  ./migrate.sh status"
    echo "  ./migrate.sh migrate"
    echo
    exit 1
fi

case "$1" in
    "status")
        echo "Checking migration status..."
        ./gradlew dbInfo
        ;;
    "migrate")
        echo "Running migrations..."
        ./gradlew dbMigrate
        ;;
    "info")
        echo "Getting migration information..."
        ./gradlew dbInfo
        ;;
    "repair")
        echo "Repairing migration metadata..."
        ./gradlew dbRepair
        ;;
    "clean")
        echo "WARNING: This will drop all tables in your database!"
        read -p "Are you sure? Type 'yes' to continue: " confirm
        if [ "$confirm" = "yes" ]; then
            echo "Cleaning database..."
            ./gradlew dbClean
        else
            echo "Operation cancelled."
        fi
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use './migrate.sh' without parameters to see available commands."
        exit 1
        ;;
esac

echo

