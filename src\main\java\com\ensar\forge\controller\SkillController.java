package com.ensar.forge.controller;

import com.ensar.forge.dto.SkillRequest;
import com.ensar.forge.dto.SkillResponse;
import com.ensar.forge.service.SkillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/skills")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Skill Management", description = "APIs for managing skills")
public class SkillController {

    private final SkillService skillService;

    @PostMapping
    @Operation(
        summary = "Create a new skill",
        description = "Creates a new skill with the provided name and description"
    )
    public ResponseEntity<SkillResponse> createSkill(
            @Parameter(description = "Skill details", required = true)
            @Valid @RequestBody SkillRequest request) {
        SkillResponse response = skillService.createSkill(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update an existing skill",
        description = "Updates a skill with the provided name and description"
    )
    public ResponseEntity<SkillResponse> updateSkill(
            @Parameter(description = "Skill ID", required = true, example = "uuid-here")
            @PathVariable String id, 
            @Parameter(description = "Updated skill details", required = true)
            @Valid @RequestBody SkillRequest request) {
        SkillResponse response = skillService.updateSkill(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get skill by ID",
        description = "Retrieves a specific skill by its ID"
    )
    public ResponseEntity<SkillResponse> getSkill(
            @Parameter(description = "Skill ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        SkillResponse response = skillService.getSkill(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all skills",
        description = "Retrieves a list of all available skills"
    )
    public ResponseEntity<List<SkillResponse>> getAllSkills() {
        List<SkillResponse> skills = skillService.getAllSkills();
        return ResponseEntity.ok(skills);
    }

    @GetMapping("/search")
    @Operation(
        summary = "Search skills by name",
        description = "Searches for skills whose names contain the specified keyword (case-insensitive)"
    )
    public ResponseEntity<List<SkillResponse>> searchSkillsByName(
            @Parameter(description = "Search keyword", required = true, example = "java")
            @RequestParam String name) {
        List<SkillResponse> skills = skillService.searchSkillsByName(name);
        return ResponseEntity.ok(skills);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a skill",
        description = "Permanently deletes a skill. Note: This will also remove all project associations."
    )
    public ResponseEntity<Void> deleteSkill(
            @Parameter(description = "Skill ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        skillService.deleteSkill(id);
        return ResponseEntity.noContent().build();
    }
}
