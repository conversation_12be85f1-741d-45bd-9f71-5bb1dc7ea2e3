package com.ensar.forge;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
public class FlywayMigrationTest {

    @Autowired
    private Flyway flyway;

    @Test
    public void testFlywayInitialization() {
        assertNotNull(flyway, "Flyway should be initialized");
    }

    @Test
    public void testMigrationInfo() {
        MigrationInfoService migrationInfoService = flyway.info();
        assertNotNull(migrationInfoService, "MigrationInfoService should be available");
        
        MigrationInfo[] allMigrations = migrationInfoService.all();
        assertNotNull(allMigrations, "Migration info should be available");
        
        // Should have at least 3 migrations (V1, V2, V3)
        assertTrue(allMigrations.length >= 3, "Should have at least 3 migrations");
        
        // Check if migrations are in correct order
        for (int i = 0; i < allMigrations.length - 1; i++) {
            String currentVersion = allMigrations[i].getVersion().getVersion();
            String nextVersion = allMigrations[i + 1].getVersion().getVersion();
            assertTrue(
                currentVersion.compareTo(nextVersion) < 0,
                "Migrations should be in ascending order: " + currentVersion + " < " + nextVersion
            );
        }
    }

    @Test
    public void testCurrentVersion() {
        MigrationInfoService migrationInfoService = flyway.info();
        MigrationInfo current = migrationInfoService.current();
        
        // In a test environment, migrations might not be applied yet
        // So we just check that the service works
        assertNotNull(migrationInfoService, "MigrationInfoService should work");
    }
}

