package com.ensar.forge.dto;

import com.ensar.forge.entity.Skill;
import com.ensar.forge.entity.UserSkill;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class UserSkillResponse {
    private String id;
    private String userId;
    private String skillId;
    private Skill skill;
    private String skillName;
    private String skillDescription;
    private String proficiencyLevel;
    private Timestamp createdDateTime;
    private Timestamp lastUpdatedDateTime;
}
