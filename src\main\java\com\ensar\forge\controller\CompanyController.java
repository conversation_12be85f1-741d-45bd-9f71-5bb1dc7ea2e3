package com.ensar.forge.controller;

import com.ensar.forge.dto.CompanyRequest;
import com.ensar.forge.dto.CompanyResponse;
import com.ensar.forge.service.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/companies")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Company Management", description = "APIs for managing companies")
public class CompanyController {

    private final CompanyService companyService;

    @PostMapping
    @Operation(
        summary = "Create a new company",
        description = "Creates a new company with the provided details"
    )
    public ResponseEntity<CompanyResponse> createCompany(
            @Parameter(description = "Company details", required = true)
            @Valid @RequestBody CompanyRequest request) {
        CompanyResponse response = companyService.createCompany(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update an existing company",
        description = "Updates a company with the provided details"
    )
    public ResponseEntity<CompanyResponse> updateCompany(
            @Parameter(description = "Company ID", required = true, example = "uuid-here")
            @PathVariable String id, 
            @Parameter(description = "Updated company details", required = true)
            @Valid @RequestBody CompanyRequest request) {
        CompanyResponse response = companyService.updateCompany(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get company by ID",
        description = "Retrieves a specific company by its ID"
    )
    public ResponseEntity<CompanyResponse> getCompany(
            @Parameter(description = "Company ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        CompanyResponse response = companyService.getCompany(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all companies",
        description = "Retrieves a list of all companies"
    )
    public ResponseEntity<List<CompanyResponse>> getAllCompanies() {
        List<CompanyResponse> companies = companyService.getAllCompanies();
        return ResponseEntity.ok(companies);
    }

    @GetMapping("/active")
    public ResponseEntity<List<CompanyResponse>> getActiveCompanies() {
        List<CompanyResponse> companies = companyService.getActiveCompanies();
        return ResponseEntity.ok(companies);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a company",
        description = "Permanently deletes a company"
    )
    public ResponseEntity<Void> deleteCompany(
            @Parameter(description = "Company ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        companyService.deleteCompany(id);
        return ResponseEntity.noContent().build();
    }
}
