# Project Filtering with Budget Ranges

This document describes the updated project filtering functionality that supports budget range filtering.

## Overview

The GET `/api/projects` endpoint now supports filtering by budget ranges using human-readable budget range strings that are automatically converted to min/max budget values.

## API Endpoints

### Main Filtering Endpoint
```
GET /api/projects?page=0&size=25&searchTerm=project&priority=LOW,HIGH&budget=Under $10K,$10K - $25K,Over $100K&location=New York,San Francisco&deadline=2025-12-31,2025-01-01
```

### Get All Unique Locations
```
GET /api/projects/locations
```
Returns a list of all unique locations from the projects table. Useful for populating dropdown menus or filter options.

## Query Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | int | Page number (0-based) | `0` |
| `size` | int | Page size | `25` |
| `searchTerm` | string | Search term for title or description | `project` |
| `priority` | string | Priority filter(s) - comma-separated | `LOW,HIGH` |
| `budget` | string | Budget range filter(s) - comma-separated | `Under $10K,$10K - $25K,Over $100K` |
| `location` | string | Location filter(s) - comma-separated | `New York,San Francisco` |
| `deadline` | string | Deadline filter(s) - comma-separated (YYYY-MM-DD) | `2025-12-31,2025-01-01` |

## Filter Values

### Priority
- `LOW`
- `MEDIUM` 
- `HIGH`

### Budget Ranges
The system supports various budget range formats that are automatically parsed:

#### Supported Formats:
- **"Under $X"** - Projects with max budget less than X
  - Example: `Under $10K` → max budget < $10,000
- **"$X - $Y"** - Projects with budget between X and Y
  - Example: `$10K - $25K` → min budget ≥ $10,000 AND max budget ≤ $25,000
- **"Over $X"** - Projects with min budget greater than X
  - Example: `Over $100K` → min budget > $100,000
- **"$X+"** - Projects with min budget greater than or equal to X
  - Example: `$50K+` → min budget ≥ $50,000
- **"$X"** - Projects with min budget greater than or equal to X
  - Example: `$25K` → min budget ≥ $25,000

#### Budget Range Examples:
- `Under $10K` - Projects under $10,000
- `$10K - $25K` - Projects between $10,000 and $25,000
- `$25K - $50K` - Projects between $25,000 and $50,000
- `$50K - $100K` - Projects between $50,000 and $100,000
- `Over $100K` - Projects over $100,000
- `$50K+` - Projects $50,000 and above

### Location
Any string value (case-insensitive partial match)

### Deadline
Date in YYYY-MM-DD format

## Examples

### Filter by multiple priorities
```
GET /api/projects?priority=LOW,HIGH
```

### Filter by single budget range
```
GET /api/projects?budget=Under $10K
```

### Filter by multiple budget ranges
```
GET /api/projects?budget=Under $10K,$10K - $25K,Over $100K
```

### Filter by budget range with other filters
```
GET /api/projects?budget=$25K - $50K&priority=HIGH&location=Remote
```

### Filter by multiple locations
```
GET /api/projects?location=New York,San Francisco,Remote
```

### Filter by multiple deadlines
```
GET /api/projects?deadline=2025-12-31,2025-01-01
```

### Combined filtering
```
GET /api/projects?page=0&size=10&priority=HIGH&budget=Over $50K&location=Remote
```

### Get all available locations
```
GET /api/projects/locations
```

## Implementation Details

- Multiple values are separated by commas (for priority, budget, location, deadline)
- Values are trimmed of leading/trailing whitespace
- Empty values after trimming are ignored
- Invalid values (e.g., invalid priority, date format, or budget format) are ignored
- Budget ranges are automatically converted to min/max budget values for database filtering
- The system finds the overall min and max from all provided budget ranges
- Search term still uses LIKE for partial matching

## Budget Range Processing

When multiple budget ranges are provided, the system:
1. Parses each range individually
2. Extracts min and max values from each range
3. Finds the overall minimum from all min values
4. Finds the overall maximum from all max values
5. Uses these overall min/max values for database filtering

**Example:**
- Input: `budget=Under $10K,$25K - $50K,Over $100K`
- Parsed ranges:
  - "Under $10K" → max: $9,999.99
  - "$25K - $50K" → min: $25,000, max: $50,000
  - "Over $100K" → min: $100,000.01
- Final filter: min budget ≥ $25,000 AND max budget ≤ $50,000

## Error Handling

- Invalid priority values are ignored (filter not applied)
- Invalid date formats are ignored (filter not applied)
- Invalid budget range formats are ignored (filter not applied)
- Empty or null filter values are ignored (filter not applied)
- The endpoint will return results even if some filters are invalid

## Database Query

The filtering is implemented using a JPA query with comparison operators for budget:

```sql
SELECT DISTINCT p FROM Project p
LEFT JOIN FETCH p.projectSkills ps
LEFT JOIN FETCH ps.skill
WHERE (:searchTerm IS NULL OR 
       p.title LIKE %:searchTerm% OR 
       p.description LIKE %:searchTerm%)
AND (:priorities IS NULL OR p.priority IN :priorities)
AND (:minBudget IS NULL OR p.minBudget >= :minBudget)
AND (:maxBudget IS NULL OR p.maxBudget <= :maxBudget)
AND (:locations IS NULL OR p.location IN :locations)
AND (:deadlines IS NULL OR p.deadline IN :deadlines)
```

The locations endpoint uses:

```sql
SELECT DISTINCT p.location FROM Project p WHERE p.location IS NOT NULL ORDER BY p.location
```
