package com.ensar.forge.repository;

import com.ensar.forge.entity.Proposal;
import com.ensar.forge.entity.Proposal.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProposalRepository extends JpaRepository<Proposal, String> {
    
    List<Proposal> findByProjectId(String projectId);
    
    List<Proposal> findByUserId(String userId);
    
    List<Proposal> findByStatus(Status status);
    
    List<Proposal> findByProjectIdAndUserId(String projectId, String userId);
    
    @Query("SELECT p FROM Proposal p WHERE p.project.id = :projectId AND p.status = :status")
    List<Proposal> findByProjectIdAndStatus(@Param("projectId") String projectId, @Param("status") Status status);
    
    @Query("SELECT p FROM Proposal p WHERE p.user.id = :userId AND p.status = :status")
    List<Proposal> findByUserIdAndStatus(@Param("userId") String userId, @Param("status") Status status);
    
    @Query("""
        SELECT DISTINCT p FROM Proposal p
        LEFT JOIN FETCH p.projectPhases
        LEFT JOIN FETCH p.attachments
        WHERE p.id = :id
        """)
    Optional<Proposal> findByIdWithPhasesAndAttachments(@Param("id") String id);
    
    @Query("""
        SELECT DISTINCT p FROM Proposal p
        LEFT JOIN FETCH p.projectPhases
        LEFT JOIN FETCH p.attachments
        WHERE p.project.id = :projectId
        """)
    List<Proposal> findByProjectIdWithPhasesAndAttachments(@Param("projectId") String projectId);
    
    @Query("""
        SELECT DISTINCT p FROM Proposal p
        LEFT JOIN FETCH p.projectPhases
        LEFT JOIN FETCH p.attachments
        WHERE p.user.id = :userId
        """)
    List<Proposal> findByUserIdWithPhasesAndAttachments(@Param("userId") String userId);
    
    @Query("""
        SELECT DISTINCT p FROM Proposal p
        LEFT JOIN FETCH p.projectPhases
        LEFT JOIN FETCH p.attachments
        WHERE (:projectId IS NULL OR p.project.id = :projectId)
        AND (:userId IS NULL OR p.user.id = :userId)
        AND (:status IS NULL OR p.status = :status)
        AND (:minBudget IS NULL OR p.totalBudget >= :minBudget)
        AND (:maxBudget IS NULL OR p.totalBudget <= :maxBudget)
        AND (:minTimeline IS NULL OR p.timelineWeeks >= :minTimeline)
        AND (:maxTimeline IS NULL OR p.timelineWeeks <= :maxTimeline)
        AND (:currentUserId IS NULL OR p.user.id = :currentUserId)
        AND (:projectOwnerId IS NULL OR p.project.user.id = :projectOwnerId)
        AND (:searchTerm IS NULL OR LOWER(p.project.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')))
        """)
    Page<Proposal> findAllWithFilters(
            @Param("projectId") String projectId,
            @Param("userId") String userId,
            @Param("status") Status status,
            @Param("minBudget") java.math.BigDecimal minBudget,
            @Param("maxBudget") java.math.BigDecimal maxBudget,
            @Param("minTimeline") Integer minTimeline,
            @Param("maxTimeline") Integer maxTimeline,
            @Param("currentUserId") String currentUserId,
            @Param("projectOwnerId") String projectOwnerId,
            @Param("searchTerm") String searchTerm,
            Pageable pageable);
    
    @Query("SELECT COUNT(p) FROM Proposal p WHERE p.project.id = :projectId AND p.status = :status")
    Long countByProjectIdAndStatus(@Param("projectId") String projectId, @Param("status") Status status);
    
    @Query("SELECT COUNT(p) FROM Proposal p WHERE p.user.id = :userId AND p.status = :status")
    Long countByUserIdAndStatus(@Param("userId") String userId, @Param("status") Status status);
    
    boolean existsByProjectIdAndUserId(String projectId, String userId);
    
    long countByStatus(Proposal.Status status);
    
    long countByStatusIn(List<Proposal.Status> statuses);
    
    @Query("SELECT COUNT(p) FROM Proposal p WHERE p.user.id = :userId AND p.status IN :statuses")
    long countByUserIdAndStatusIn(@Param("userId") String userId, @Param("statuses") List<Proposal.Status> statuses);
}
