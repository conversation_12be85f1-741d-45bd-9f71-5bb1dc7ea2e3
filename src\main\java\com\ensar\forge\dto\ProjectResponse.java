package com.ensar.forge.dto;

import com.ensar.forge.entity.Project;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import com.ensar.forge.entity.Project.Priority;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

@Data
@Schema(description = "Response object containing project information")
public class ProjectResponse {
    @Schema(description = "Unique project identifier", example = "uuid-here")
    private String id;
    
    @Schema(description = "Project title", example = "Web Application Development")
    private String title;
    
    @Schema(description = "Project description", example = "A modern web application using React and Spring Boot")
    private String description;

    @Schema(description = "Project status")
    private Project.Status status;

    @Schema(description = "Minimum budget for the project", example = "20000.00")
    private BigDecimal minBudget;

    @Schema(description = "Maximum budget for the project", example = "30000.00")
    private BigDecimal maxBudget;
    
    @Schema(description = "Project location", example = "Remote")
    private String location;
    
    @Schema(description = "Project deadline", example = "2024-06-30")
    private Date deadline;
    
    @Schema(description = "Project priority level", example = "HIGH")
    private Priority priority;
    
    @Schema(description = "Timestamp when the project was created", example = "2024-01-15T10:30:00")
    private Timestamp createdDateTime;
    
    @Schema(description = "Timestamp when the project was last updated", example = "2024-01-15T15:45:00")
    private Timestamp lastUpdatedDateTime;
    
    @Schema(description = "List of skills required for this project")
    private List<SkillResponse> skills;
    
    @Schema(description = "User information who created/owns this project")
    private UserResponse user;
}
