package com.ensar.forge.service;

import com.ensar.forge.dto.DashboardResponse;
import com.ensar.forge.entity.Project;
import com.ensar.forge.entity.Proposal;
import com.ensar.forge.entity.User;
import com.ensar.forge.repository.ProjectRepository;
import com.ensar.forge.repository.ProposalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DashboardService {
    
    private final ProjectRepository projectRepository;
    private final ProposalRepository proposalRepository;
    
    public DashboardResponse getDashboardMetrics(User user) {
        if (user.getRole() == User.Role.REQUESTING_SI) {
            return getRequestingSIMetrics();
        } else if (user.getRole() == User.Role.PROVIDING_SI) {
            return getProvidingSIMetrics(user.getId());
        } else {
            // For ADMIN and SUPER_ADMIN, return requesting SI metrics by default
            return getRequestingSIMetrics();
        }
    }
    
    private DashboardResponse getRequestingSIMetrics() {
        // Count active projects (status = ACTIVE)
        long activeProjects = projectRepository.countByStatus(Project.Status.ACTIVE);
        
        // Count total proposals
        long totalProposals = proposalRepository.count();
        
        // Count completed projects (status = COMPLETED)
        long completedProjects = projectRepository.countByStatus(Project.Status.COMPLETED);
        
        return new DashboardResponse(activeProjects, totalProposals, completedProjects);
    }
    
    private DashboardResponse getProvidingSIMetrics(String userId) {
        // Count active proposals (status = UNDER_REVIEW or PENDING)
        List<Proposal.Status> activeProposalStatuses = Arrays.asList(
            Proposal.Status.UNDER_REVIEW, 
            Proposal.Status.PENDING
        );
        long activeProposals = proposalRepository.countByUserIdAndStatusIn(userId, activeProposalStatuses);
        
        // Count won projects (proposals with status = APPROVED or IN_PROGRESS)
        List<Proposal.Status> wonProposalStatuses = Arrays.asList(
            Proposal.Status.APPROVED, 
            Proposal.Status.IN_PROGRESS
        );
        long wonProjects = proposalRepository.countByUserIdAndStatusIn(userId, wonProposalStatuses);
        
        // Profile views - placeholder for now (would need to be implemented with tracking)
        long profileViews = 0L; // TODO: Implement profile view tracking
        
        return DashboardResponse.forProvidingSI(activeProposals, wonProjects, profileViews);
    }
}
