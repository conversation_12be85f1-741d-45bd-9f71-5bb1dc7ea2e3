package com.ensar.forge.service;

import com.ensar.forge.dto.ProjectSkillRequest;
import com.ensar.forge.dto.ProjectSkillResponse;
import com.ensar.forge.entity.Project;
import com.ensar.forge.entity.ProjectSkill;
import com.ensar.forge.entity.Skill;
import com.ensar.forge.repository.ProjectRepository;
import com.ensar.forge.repository.ProjectSkillRepository;
import com.ensar.forge.repository.SkillRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProjectSkillService {

    private final ProjectSkillRepository projectSkillRepository;
    private final ProjectRepository projectRepository;
    private final SkillRepository skillRepository;

    public ProjectSkillResponse addSkillToProject(ProjectSkillRequest request) {
        Project project = projectRepository.findById(request.getProjectId())
                .orElseThrow(() -> new RuntimeException("Project not found with id: " + request.getProjectId()));

        Skill skill = skillRepository.findById(request.getSkillId())
                .orElseThrow(() -> new RuntimeException("Skill not found with id: " + request.getSkillId()));

        if (projectSkillRepository.existsByProjectIdAndSkillId(request.getProjectId(), request.getSkillId())) {
            throw new RuntimeException("Skill is already associated with this project");
        }

        ProjectSkill projectSkill = new ProjectSkill();
        projectSkill.setProject(project);
        projectSkill.setSkill(skill);

        ProjectSkill savedProjectSkill = projectSkillRepository.save(projectSkill);
        return mapToResponse(savedProjectSkill);
    }

    public void removeSkillFromProject(String projectId, String skillId) {
        if (!projectSkillRepository.existsByProjectIdAndSkillId(projectId, skillId)) {
            throw new RuntimeException("Project skill relationship not found");
        }
        projectSkillRepository.deleteByProjectIdAndSkillId(projectId, skillId);
    }

    public List<ProjectSkillResponse> getProjectSkills(String projectId) {
        List<ProjectSkill> projectSkills = projectSkillRepository.findByProjectIdWithDetails(projectId);
        return projectSkills.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<ProjectSkillResponse> getSkillsByProject(String projectId) {
        List<ProjectSkill> projectSkills = projectSkillRepository.findByProjectIdWithDetails(projectId);
        return projectSkills.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<ProjectSkillResponse> getProjectsBySkill(String skillId) {
        List<ProjectSkill> projectSkills = projectSkillRepository.findBySkillIdWithDetails(skillId);
        return projectSkills.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public ProjectSkillResponse getProjectSkill(String id) {
        ProjectSkill projectSkill = projectSkillRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Project skill not found with id: " + id));
        return mapToResponse(projectSkill);
    }

    public List<ProjectSkillResponse> getAllProjectSkills() {
        return projectSkillRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    private ProjectSkillResponse mapToResponse(ProjectSkill projectSkill) {
        ProjectSkillResponse response = new ProjectSkillResponse();
        response.setId(projectSkill.getId());
        response.setProjectId(projectSkill.getProject().getId());
        response.setSkillId(projectSkill.getSkill().getId());
        response.setProjectTitle(projectSkill.getProject().getTitle());
        response.setSkillName(projectSkill.getSkill().getName());
        response.setCreatedDateTime(projectSkill.getCreatedDateTime());
        response.setLastUpdatedDateTime(projectSkill.getLastUpdatedDateTime());
        return response;
    }
}


