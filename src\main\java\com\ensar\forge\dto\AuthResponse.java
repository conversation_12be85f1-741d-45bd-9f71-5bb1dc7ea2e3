package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Authentication response containing JWT tokens and user information")
public class AuthResponse {

    @Schema(description = "JWT access token for API authentication", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "Response message indicating success or failure", example = "Authentication successful")
    private String message;

    @Schema(description = "User information including skills")
    private UserResponse user;
}