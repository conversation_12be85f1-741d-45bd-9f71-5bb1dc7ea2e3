package com.ensar.forge.controller;

import com.ensar.forge.dto.UserSkillRequest;
import com.ensar.forge.dto.UserSkillResponse;
import com.ensar.forge.service.UserSkillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/user-skills")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "User-Skill Management", description = "APIs for managing user skills and proficiency levels")
public class UserSkillController {

    private final UserSkillService userSkillService;

    @Operation(
            summary = "Create new user skill based on user",
            description = "Create new user skills for specific user"
    )
    @PostMapping("/users/{userId}/skills")
    public ResponseEntity<UserSkillResponse> addSkillToUser(@PathVariable String userId,
                                                          @Valid @RequestBody UserSkillRequest request) {
        UserSkillResponse response = userSkillService.addSkillToUser(userId, request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<UserSkillResponse> updateUserSkill(@PathVariable String id,
                                                           @Valid @RequestBody UserSkillRequest request) {
        UserSkillResponse response = userSkillService.updateUserSkill(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserSkillResponse> getUserSkillById(@PathVariable String id) {
        UserSkillResponse response = userSkillService.getUserSkillById(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/users/{userId}/skills")
    public ResponseEntity<List<UserSkillResponse>> getUserSkills(@PathVariable String userId) {
        List<UserSkillResponse> userSkills = userSkillService.getUserSkills(userId);
        return ResponseEntity.ok(userSkills);
    }

    @DeleteMapping("/users/{userId}/skills/{skillId}")
    public ResponseEntity<Void> removeSkillFromUser(@PathVariable String userId, @PathVariable String skillId) {
        userSkillService.removeSkillFromUser(userId, skillId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/skills/{skillId}/users")
    public ResponseEntity<List<UserSkillResponse>> getUsersBySkill(@PathVariable String skillId) {
        List<UserSkillResponse> users = userSkillService.getUsersBySkill(skillId);
        return ResponseEntity.ok(users);
    }
}
