package com.ensar.forge.repository;

import com.ensar.forge.entity.Company;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, String> {
    
    Optional<Company> findByName(String name);
    
    Optional<Company> findByDomain(String domain);
    
    List<Company> findByDisabled(Boolean disabled);
    
    boolean existsByName(String name);
    
    boolean existsByDomain(String domain);
}


