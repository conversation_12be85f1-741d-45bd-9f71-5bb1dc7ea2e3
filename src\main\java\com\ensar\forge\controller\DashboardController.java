package com.ensar.forge.controller;

import com.ensar.forge.dto.DashboardResponse;
import com.ensar.forge.entity.User;
import com.ensar.forge.service.DashboardService;
import com.ensar.forge.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@Tag(name = "Dashboard", description = "Dashboard metrics endpoints")
public class DashboardController {
    
    private final DashboardService dashboardService;

    private final UserService userService;
    
    @GetMapping("/metrics")
    @Operation(summary = "Get dashboard metrics", description = "Retrieves role-based dashboard metrics - project metrics for REQUESTING_SI, proposal metrics for PROVIDING_SI")
    public ResponseEntity<DashboardResponse> getDashboardMetrics(Authentication authentication) {
        User currentUser = userService.getCurrentUser();
        DashboardResponse metrics = dashboardService.getDashboardMetrics(currentUser);
        return ResponseEntity.ok(metrics);
    }
}
