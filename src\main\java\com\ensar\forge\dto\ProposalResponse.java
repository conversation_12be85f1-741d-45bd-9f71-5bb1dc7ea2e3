package com.ensar.forge.dto;

import com.ensar.forge.entity.Proposal.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Data
@Schema(description = "Response object containing proposal information")
public class ProposalResponse {
    
    @Schema(description = "Unique proposal identifier", example = "uuid-here")
    private String id;
    
    @Schema(description = "Project information for which the proposal is submitted")
    private ProjectResponse project;
    
    @Schema(description = "User who submitted the proposal")
    private UserResponse user;
    
    @Schema(description = "Proposal status", example = "UNDER_REVIEW")
    private Status status;
    
    @Schema(description = "Total budget for the proposal", example = "50000.00")
    private BigDecimal totalBudget;
    
    @Schema(description = "Timeline in weeks", example = "12")
    private Integer timelineWeeks;
    
    @Schema(description = "Detailed description of the proposal", example = "This proposal outlines a comprehensive solution...")
    private String description;
    
    @Schema(description = "List of deliverables as JSON string", example = "[\"Web Application\", \"Mobile App\", \"Documentation\"]")
    private String deliverables;
    
    @Schema(description = "Approach and methodology", example = "We will use Agile methodology with weekly sprints...")
    private String approachMethodology;
    
    @Schema(description = "Relevant experience and qualifications", example = "Our team has 10+ years of experience...")
    private String relevantExperience;

    @Schema(description = "Rejection Reason")
    private String rejectionReason;
    
    @Schema(description = "Timestamp when the proposal was reviewed", example = "2024-01-15T10:30:00")
    private Timestamp reviewedAt;
    
    @Schema(description = "User who reviewed the proposal")
    private UserResponse reviewedBy;
    
    @Schema(description = "Revision number of the proposal", example = "1")
    private Integer revisionNumber;
    
    @Schema(description = "Timestamp when the proposal was created", example = "2024-01-15T10:30:00")
    private Timestamp createdDateTime;
    
    @Schema(description = "Timestamp when the proposal was last updated", example = "2024-01-15T15:45:00")
    private Timestamp lastUpdatedDateTime;
    
    @Schema(description = "List of project phases")
    private List<ProjectPhaseResponse> projectPhases;
    
    @Schema(description = "List of proposal attachments")
    private List<ProposalAttachmentResponse> attachments;
}
