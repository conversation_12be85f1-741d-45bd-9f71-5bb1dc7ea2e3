package com.ensar.forge.service;

import com.ensar.forge.dto.ProposalAttachmentRequest;
import com.ensar.forge.dto.ProposalAttachmentResponse;
import com.ensar.forge.entity.Proposal;
import com.ensar.forge.entity.ProposalAttachment;
import com.ensar.forge.repository.ProposalAttachmentRepository;
import com.ensar.forge.repository.ProposalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProposalAttachmentService {

    private final ProposalAttachmentRepository proposalAttachmentRepository;
    private final ProposalRepository proposalRepository;

    public ProposalAttachmentResponse createAttachment(ProposalAttachmentRequest request) {
        Proposal proposal = proposalRepository.findById(request.getProposalId())
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + request.getProposalId()));

        MultipartFile file = request.getFile();
        
        // Validate file
        if (file.isEmpty()) {
            throw new RuntimeException("File cannot be empty");
        }

        // Check if file with same name already exists for this proposal
        if (proposalAttachmentRepository.findByProposalIdAndFileName(request.getProposalId(), file.getOriginalFilename()).isPresent()) {
            throw new RuntimeException("File with name '" + file.getOriginalFilename() + "' already exists for this proposal");
        }

        try {
            ProposalAttachment attachment = new ProposalAttachment();
            attachment.setProposal(proposal);
            attachment.setFileName(file.getOriginalFilename());
            attachment.setFileType(file.getContentType());
            attachment.setData(file.getBytes());

            ProposalAttachment savedAttachment = proposalAttachmentRepository.save(attachment);
            return mapToResponse(savedAttachment);
        } catch (IOException e) {
            throw new RuntimeException("Error processing file: " + e.getMessage());
        }
    }

    public ProposalAttachmentResponse getAttachment(String id) {
        ProposalAttachment attachment = proposalAttachmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Attachment not found with id: " + id));
        return mapToResponse(attachment);
    }

    public List<ProposalAttachmentResponse> getAttachmentsByProposal(String proposalId) {
        return proposalAttachmentRepository.findByProposalIdOrderByCreatedDateTime(proposalId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public byte[] getAttachmentData(String id) {
        ProposalAttachment attachment = proposalAttachmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Attachment not found with id: " + id));
        return attachment.getData();
    }

    public void deleteAttachment(String id) {
        if (!proposalAttachmentRepository.existsById(id)) {
            throw new RuntimeException("Attachment not found with id: " + id);
        }
        proposalAttachmentRepository.deleteById(id);
    }

    public void deleteAttachmentsByProposal(String proposalId) {
        proposalAttachmentRepository.deleteByProposalId(proposalId);
    }

    public Long getAttachmentCountByProposal(String proposalId) {
        return proposalAttachmentRepository.countByProposalId(proposalId);
    }

    private ProposalAttachmentResponse mapToResponse(ProposalAttachment attachment) {
        ProposalAttachmentResponse response = new ProposalAttachmentResponse();
        response.setId(attachment.getId());
        response.setFileName(attachment.getFileName());
        response.setFileType(attachment.getFileType());
        response.setFileSize((long) attachment.getData().length);
        response.setCreatedDateTime(attachment.getCreatedDateTime());
        response.setLastUpdatedDateTime(attachment.getLastUpdatedDateTime());
        return response;
    }
}
