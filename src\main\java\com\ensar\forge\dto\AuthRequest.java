package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Authentication request containing user credentials")
public class AuthRequest {
    
    @Schema(description = "User's email address", example = "<EMAIL>", required = true)
    private String email;
    
    @Schema(description = "User's password", example = "password123", required = true)
    private String password;
}
