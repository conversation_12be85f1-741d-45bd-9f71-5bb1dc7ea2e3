package com.ensar.forge.repository;

import com.ensar.forge.entity.ProposalAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProposalAttachmentRepository extends JpaRepository<ProposalAttachment, String> {
    
    List<ProposalAttachment> findByProposalId(String proposalId);
    
    @Query("SELECT pa FROM ProposalAttachment pa WHERE pa.proposal.id = :proposalId ORDER BY pa.createdDateTime")
    List<ProposalAttachment> findByProposalIdOrderByCreatedDateTime(@Param("proposalId") String proposalId);
    
    Optional<ProposalAttachment> findByProposalIdAndFileName(String proposalId, String fileName);
    
    void deleteByProposalId(String proposalId);
    
    @Query("SELECT COUNT(pa) FROM ProposalAttachment pa WHERE pa.proposal.id = :proposalId")
    Long countByProposalId(@Param("proposalId") String proposalId);
}
