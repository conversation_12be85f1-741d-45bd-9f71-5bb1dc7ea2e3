package com.ensar.forge.repository;

import com.ensar.forge.entity.ProjectSkill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectSkillRepository extends JpaRepository<ProjectSkill, String> {
    
    List<ProjectSkill> findByProjectId(String projectId);
    
    List<ProjectSkill> findBySkillId(String skillId);
    
    Optional<ProjectSkill> findByProjectIdAndSkillId(String projectId, String skillId);
    
    boolean existsByProjectIdAndSkillId(String projectId, String skillId);
    
    void deleteByProjectIdAndSkillId(String projectId, String skillId);
    
    @Query("SELECT ps FROM ProjectSkill ps JOIN FETCH ps.project JOIN FETCH ps.skill WHERE ps.project.id = :projectId")
    List<ProjectSkill> findByProjectIdWithDetails(@Param("projectId") String projectId);
    
    @Query("SELECT ps FROM ProjectSkill ps JOIN FETCH ps.project JOIN FETCH ps.skill WHERE ps.skill.id = :skillId")
    List<ProjectSkill> findBySkillIdWithDetails(@Param("skillId") String skillId);
}


