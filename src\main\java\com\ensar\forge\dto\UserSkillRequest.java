package com.ensar.forge.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class UserSkillRequest {
    
    @NotBlank(message = "Skill ID is required")
    private String skillId;
    
    @NotNull(message = "Proficiency level is required")
    private com.ensar.forge.entity.UserSkill.ProficiencyLevel proficiencyLevel;
}
