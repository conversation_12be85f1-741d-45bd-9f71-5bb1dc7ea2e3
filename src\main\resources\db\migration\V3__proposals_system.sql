-- Create proposals table
CREATE TABLE proposals (
    id CHAR(36) PRIMARY KEY,
    project_id CHAR(36) NOT NULL,
    user_id CHAR(36) NOT NULL,
    status ENUM('DRAFT','PENDING','UNDER_REVIEW', 'APPROVED', 'REJECTED') NOT NULL DEFAULT 'PENDING',
    total_budget DECIMAL(15,2) NOT NULL,
    timeline_weeks INT NOT NULL,
    description TEXT NOT NULL,
    deliverables JSON, -- array of strings
    approach_methodology TEXT,
    relevant_experience TEXT,
    rejection_reason TEXT,
    reviewed_at TIMESTAMP NULL,
    reviewed_by CHAR(36) NULL,
    revision_number INT DEFAULT 1,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,

    -- Relationships
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (reviewed_by) REFERENCES users(id)
);

-- Create project_phases table
CREATE TABLE project_phases (
    id CHAR(36) PRIMARY KEY,
    proposal_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    duration_weeks INT,
    cost DECIMAL(15,2),
    is_deleted BOOLEAN DEFAULT FALSE,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,

    FOREIGN KEY (proposal_id) REFERENCES proposals(id)
);

-- Create proposal_attachments table
CREATE TABLE proposal_attachments (
    id CHAR(36) PRIMARY KEY,
    proposal_id CHAR(36) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50),
    data LONGBLOB NOT NULL,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (proposal_id) REFERENCES proposals(id) ON DELETE CASCADE
);
