package com.ensar.forge.service;

import com.ensar.forge.dto.ProjectRequest;
import com.ensar.forge.dto.ProjectResponse;
import com.ensar.forge.dto.SkillResponse;
import com.ensar.forge.dto.UserResponse;
import com.ensar.forge.entity.Project;
import com.ensar.forge.entity.ProjectSkill;
import com.ensar.forge.entity.Skill;
import com.ensar.forge.entity.User;
import com.ensar.forge.exception.ResourceNotFoundException;
import com.ensar.forge.exception.ResourceAlreadyExistsException;
import com.ensar.forge.repository.ProjectRepository;
import com.ensar.forge.repository.ProjectSkillRepository;
import com.ensar.forge.repository.SkillRepository;
import com.ensar.forge.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProjectService {

    private final ProjectRepository projectRepository;
    private final ProjectSkillRepository projectSkillRepository;
    private final SkillRepository skillRepository;
    private final UserRepository userRepository;
    private final UserService userService;


    public ProjectResponse createProject(ProjectRequest request) {
        if (projectRepository.existsByTitle(request.getTitle())) {
            throw new ResourceAlreadyExistsException("Project", "title", request.getTitle());
        }

        User currentUser = userService.getCurrentUser();

        Project project = new Project();
        project.setTitle(request.getTitle());
        project.setDescription(request.getDescription());
        project.setStatus(request.getStatus());
        project.setMinBudget(request.getMinBudget());
        project.setMaxBudget(request.getMaxBudget());
        project.setLocation(request.getLocation());
        project.setDeadline(request.getDeadline());
        project.setPriority(request.getPriority());

        // Set user if provided
        if (currentUser.getId() != null && !currentUser.getId().isEmpty()) {
            User user = userRepository.findById(currentUser.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("User", "id", currentUser.getId()));
            project.setUser(user);
        }

        Project savedProject = projectRepository.save(project);

        // Add skills if provided
        if (request.getSkillsIds() != null && !request.getSkillsIds().isEmpty()) {
            addSkillsToProject(savedProject.getId(), request.getSkillsIds());
        }

        return mapToResponse(savedProject);
    }

    public ProjectResponse updateProject(String id, ProjectRequest request) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project", "id", id));

        User currentUser = userService.getCurrentUser();

        if (!project.getTitle().equals(request.getTitle()) && projectRepository.existsByTitle(request.getTitle())) {
            throw new ResourceAlreadyExistsException("Project", "title", request.getTitle());
        }

        project.setTitle(request.getTitle());
        project.setDescription(request.getDescription());
        project.setMinBudget(request.getMinBudget());
        project.setMaxBudget(request.getMaxBudget());
        project.setLocation(request.getLocation());
        project.setDeadline(request.getDeadline());
        project.setPriority(request.getPriority());
        project.setStatus(request.getStatus());

        // Update user if provided
        if (currentUser.getId() != null && !currentUser.getId().isEmpty()) {
            User user = userRepository.findById(currentUser.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("User", "id", currentUser.getId()));
            project.setUser(user);
        } else {
            // If userId is null or empty, remove the user association
            project.setUser(null);
        }

        Project updatedProject = projectRepository.save(project);

        // Update skills if provided
        if (request.getSkillsIds() != null) {
            updateProjectSkills(updatedProject.getId(), request.getSkillsIds());
        }

        return mapToResponse(updatedProject);
    }

    public ProjectResponse getProject(String id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project", "id", id));
        return mapToResponse(project);
    }

    public List<ProjectResponse> getAllProjects() {
        return projectRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }



    public List<ProjectResponse> getProjectsByLocation(String location) {
        return projectRepository.findByLocationContainingIgnoreCase(location).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void deleteProject(String id) {
        if (!projectRepository.existsById(id)) {
            throw new ResourceNotFoundException("Project", "id", id);
        }
        projectRepository.deleteById(id);
    }

    public void addSkillsToProject(String projectId, List<String> skillIds) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("Project", "id", projectId));

        for (String skillId : skillIds) {
            Skill skill = skillRepository.findById(skillId)
                    .orElseThrow(() -> new ResourceNotFoundException("Skill", "id", skillId));

            if (!projectSkillRepository.existsByProjectIdAndSkillId(projectId, skillId)) {
                ProjectSkill projectSkill = new ProjectSkill();
                projectSkill.setProject(project);
                projectSkill.setSkill(skill);
                projectSkillRepository.save(projectSkill);
            }
        }
    }

    public void removeSkillFromProject(String projectId, String skillId) {
        projectSkillRepository.deleteByProjectIdAndSkillId(projectId, skillId);
    }

    private void updateProjectSkills(String projectId, List<String> skillIds) {
        // Remove all existing skills
        List<ProjectSkill> existingSkills = projectSkillRepository.findByProjectId(projectId);
        projectSkillRepository.deleteAll(existingSkills);

        // Add new skills
        if (skillIds != null && !skillIds.isEmpty()) {
            addSkillsToProject(projectId, skillIds);
        }
    }

    public Page<ProjectResponse> getAllProjectsWithPagination(
            int page,
            int size,
            Optional<String> searchTerm,
            Optional<String> priority,
            Optional<String> budget,
            Optional<String> location,
            Optional<String> deadline) {

        Pageable pageable = PageRequest.of(page, size);

        User currentUser = userService.getCurrentUser();
        String currentUserId = null;
        List<Project.Status> statuses = null;

        // If user role is REQUESTING_SI, filter by current user
        if (currentUser != null) {
            if (currentUser.getRole() == User.Role.REQUESTING_SI) {
                currentUserId = currentUser.getId();
            } else if (currentUser.getRole() == User.Role.PROVIDING_SI) {
                // Only ACTIVE and PENDING for PROVIDING_SI
                statuses = java.util.Arrays.asList(Project.Status.ACTIVE, Project.Status.PENDING);
            }
        }

        // Parse priorities if provided
        List<Project.Priority> priorityEnums = null;
        if (priority.isPresent() && !priority.get().isEmpty()) {
            try {
                String[] priorityValues = priority.get().split(",");
                priorityEnums = java.util.Arrays.stream(priorityValues)
                        .map(p -> p.trim().toUpperCase())
                        .filter(p -> !p.isEmpty())
                        .map(Project.Priority::valueOf)
                        .collect(Collectors.toList());
                if (priorityEnums.isEmpty()) priorityEnums = null;
            } catch (IllegalArgumentException e) {
                priorityEnums = null; // ignore invalid priorities
            }
        }

        // Parse budget ranges if provided (overall min/max across ranges)
        java.math.BigDecimal minBudgetValue = null;
        java.math.BigDecimal maxBudgetValue = null;
        if (budget.isPresent() && !budget.get().isEmpty()) {
            try {
                String[] budgetRanges = budget.get().split(",");
                List<java.math.BigDecimal> allMin = new java.util.ArrayList<>();
                List<java.math.BigDecimal> allMax = new java.util.ArrayList<>();

                for (String range : budgetRanges) {
                    String trimmed = range.trim();
                    if (!trimmed.isEmpty()) {
                        BudgetRange parsed = parseBudgetRange(trimmed);
                        if (parsed != null) {
                            if (parsed.getMinBudget() != null) allMin.add(parsed.getMinBudget());
                            if (parsed.getMaxBudget() != null) allMax.add(parsed.getMaxBudget());
                        }
                    }
                }
                if (!allMin.isEmpty()) minBudgetValue = allMin.stream().min(java.math.BigDecimal::compareTo).orElse(null);
                if (!allMax.isEmpty()) maxBudgetValue = allMax.stream().max(java.math.BigDecimal::compareTo).orElse(null);
            } catch (Exception e) {
                minBudgetValue = null;
                maxBudgetValue = null; // ignore invalid format
            }
        }

        // Parse locations if provided
        List<String> locationList = null;
        if (location.isPresent() && !location.get().isEmpty()) {
            locationList = java.util.Arrays.stream(location.get().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
            if (locationList.isEmpty()) locationList = null;
        }

        // Parse deadlines if provided
        List<Date> deadlineDates = null;
        if (deadline.isPresent() && !deadline.get().isEmpty()) {
            try {
                deadlineDates = java.util.Arrays.stream(deadline.get().split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .map(Date::valueOf) // expects yyyy-[m]m-[d]d
                        .collect(Collectors.toList());
                if (deadlineDates.isEmpty()) deadlineDates = null;
            } catch (IllegalArgumentException e) {
                deadlineDates = null; // ignore invalid dates
            }
        }

        Page<Project> projectsPage = projectRepository.findAllWithPaginationAndFilters(
                searchTerm.map(String::trim).filter(s -> !s.isEmpty()).orElse(null),
                priorityEnums,
                minBudgetValue,
                maxBudgetValue,
                locationList,
                deadlineDates,
                currentUserId,
                statuses,
                pageable
        );

        return projectsPage.map(this::mapToResponse);
    }


    /**
     * Parse budget range string into min and max budget values
     * Supports formats like: "Under $10K", "$10K - $25K", "Over $100K", "$50K+"
     */
    private BudgetRange parseBudgetRange(String budgetRange) {
        if (budgetRange == null || budgetRange.trim().isEmpty()) {
            return null;
        }
        
        String range = budgetRange.trim();
        java.math.BigDecimal minBudget = null;
        java.math.BigDecimal maxBudget = null;
        
        try {
            // Handle "Under $X" format
            if (range.toLowerCase().startsWith("under")) {
                String amountStr = range.replaceAll("[^0-9.]", "");
                if (!amountStr.isEmpty()) {
                    java.math.BigDecimal amount = new java.math.BigDecimal(amountStr);
                    // Convert K to thousands
                    if (range.toLowerCase().contains("k")) {
                        amount = amount.multiply(new java.math.BigDecimal("1000"));
                    }
                    maxBudget = amount.subtract(new java.math.BigDecimal("0.01")); // Just under the amount
                }
            }
            // Handle "Over $X" format
            else if (range.toLowerCase().startsWith("over")) {
                String amountStr = range.replaceAll("[^0-9.]", "");
                if (!amountStr.isEmpty()) {
                    java.math.BigDecimal amount = new java.math.BigDecimal(amountStr);
                    // Convert K to thousands
                    if (range.toLowerCase().contains("k")) {
                        amount = amount.multiply(new java.math.BigDecimal("1000"));
                    }
                    minBudget = amount.add(new java.math.BigDecimal("0.01")); // Just over the amount
                }
            }
            // Handle "$X+" format
            else if (range.contains("+")) {
                String amountStr = range.replaceAll("[^0-9.]", "");
                if (!amountStr.isEmpty()) {
                    java.math.BigDecimal amount = new java.math.BigDecimal(amountStr);
                    // Convert K to thousands
                    if (range.toLowerCase().contains("k")) {
                        amount = amount.multiply(new java.math.BigDecimal("1000"));
                    }
                    minBudget = amount;
                }
            }
            // Handle "$X - $Y" format
            else if (range.contains(" - ")) {
                String[] parts = range.split(" - ");
                if (parts.length == 2) {
                    String minStr = parts[0].replaceAll("[^0-9.]", "");
                    String maxStr = parts[1].replaceAll("[^0-9.]", "");
                    
                    if (!minStr.isEmpty()) {
                        minBudget = new java.math.BigDecimal(minStr);
                        // Convert K to thousands
                        if (parts[0].toLowerCase().contains("k")) {
                            minBudget = minBudget.multiply(new java.math.BigDecimal("1000"));
                        }
                    }
                    
                    if (!maxStr.isEmpty()) {
                        maxBudget = new java.math.BigDecimal(maxStr);
                        // Convert K to thousands
                        if (parts[1].toLowerCase().contains("k")) {
                            maxBudget = maxBudget.multiply(new java.math.BigDecimal("1000"));
                        }
                    }
                }
            }
            // Handle single amount like "$25K"
            else {
                String amountStr = range.replaceAll("[^0-9.]", "");
                if (!amountStr.isEmpty()) {
                    java.math.BigDecimal amount = new java.math.BigDecimal(amountStr);
                    // Convert K to thousands
                    if (range.toLowerCase().contains("k")) {
                        amount = amount.multiply(new java.math.BigDecimal("1000"));
                    }
                    // For single amounts, treat as minimum
                    minBudget = amount;
                }
            }
        } catch (NumberFormatException e) {
            // Invalid format, return null
            return null;
        }
        
        return new BudgetRange(minBudget, maxBudget);
    }
    
    /**
     * Helper class to hold min and max budget values
     */
    private static class BudgetRange {
        private final java.math.BigDecimal minBudget;
        private final java.math.BigDecimal maxBudget;
        
        public BudgetRange(java.math.BigDecimal minBudget, java.math.BigDecimal maxBudget) {
            this.minBudget = minBudget;
            this.maxBudget = maxBudget;
        }
        
        public java.math.BigDecimal getMinBudget() {
            return minBudget;
        }
        
        public java.math.BigDecimal getMaxBudget() {
            return maxBudget;
        }
    }

    public List<String> getAllLocations() {
        return projectRepository.findAllDistinctLocations();
    }

    public List<ProjectResponse> getProjectsByUser(String userId) {
        return projectRepository.findByUserId(userId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    private ProjectResponse mapToResponse(Project project) {
        ProjectResponse response = new ProjectResponse();
        response.setId(project.getId());
        response.setTitle(project.getTitle());
        response.setDescription(project.getDescription());
        response.setMinBudget(project.getMinBudget());
        response.setMaxBudget(project.getMaxBudget());
        response.setLocation(project.getLocation());
        response.setDeadline(project.getDeadline());
        response.setPriority(project.getPriority());
        response.setStatus(project.getStatus());
        response.setCreatedDateTime(project.getCreatedDateTime());
        response.setLastUpdatedDateTime(project.getLastUpdatedDateTime());
        // Map skills
        List<SkillResponse> skills = project.getProjectSkills() != null
                ? project.getProjectSkills().stream()
                .map(ps -> {
                    SkillResponse skillResponse = new SkillResponse();
                    skillResponse.setId(ps.getSkill().getId());
                    skillResponse.setName(ps.getSkill().getName());
                    skillResponse.setDescription(ps.getSkill().getDescription());
                    skillResponse.setCreatedDateTime(ps.getSkill().getCreatedDateTime());
                    skillResponse.setLastUpdatedDateTime(ps.getSkill().getLastUpdatedDateTime());
                    return skillResponse;
                })
                .collect(Collectors.toList())
                : Collections.emptyList();  // return empty list if projectSkills is null

        response.setSkills(skills);

        // Map user information
        if (project.getUser() != null) {
            UserResponse userResponse = new UserResponse();
            userResponse.setId(project.getUser().getId());
            userResponse.setFirstName(project.getUser().getFirstName());
            userResponse.setLastName(project.getUser().getLastName());
            userResponse.setEmail(project.getUser().getEmail());
            userResponse.setRole(project.getUser().getRole());
            userResponse.setEnabled(project.getUser().getEnabled());
            userResponse.setCreatedDateTime(project.getUser().getCreatedDateTime());
            userResponse.setLastUpdatedDateTime(project.getUser().getLastUpdatedDateTime());
            response.setUser(userResponse);
        }

        return response;
    }
}
