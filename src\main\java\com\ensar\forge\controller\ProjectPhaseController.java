package com.ensar.forge.controller;

import com.ensar.forge.dto.ProjectPhaseRequest;
import com.ensar.forge.dto.ProjectPhaseResponse;
import com.ensar.forge.service.ProjectPhaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/project-phases")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Project Phase Management", description = "APIs for managing project phases within proposals")
public class ProjectPhaseController {

    private final ProjectPhaseService projectPhaseService;

    @PostMapping("/proposal/{proposalId}")
    @Operation(
        summary = "Create a new project phase",
        description = "Creates a new project phase for a specific proposal"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Project phase created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<ProjectPhaseResponse> createProjectPhase(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String proposalId,
            @Parameter(description = "Project phase details", required = true)
            @Valid @RequestBody ProjectPhaseRequest request) {
        ProjectPhaseResponse response = projectPhaseService.createProjectPhase(proposalId, request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update an existing project phase",
        description = "Updates a project phase with the provided details"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Project phase updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Project phase not found")
    })
    public ResponseEntity<ProjectPhaseResponse> updateProjectPhase(
            @Parameter(description = "Project phase ID", required = true, example = "uuid-here")
            @PathVariable String id, 
            @Parameter(description = "Updated project phase details", required = true)
            @Valid @RequestBody ProjectPhaseRequest request) {
        ProjectPhaseResponse response = projectPhaseService.updateProjectPhase(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get project phase by ID",
        description = "Retrieves a specific project phase with all its details"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Project phase retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Project phase not found")
    })
    public ResponseEntity<ProjectPhaseResponse> getProjectPhase(
            @Parameter(description = "Project phase ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProjectPhaseResponse response = projectPhaseService.getProjectPhase(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/proposal/{proposalId}")
    @Operation(
        summary = "Get project phases by proposal",
        description = "Retrieves all project phases for a specific proposal"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Project phases retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<List<ProjectPhaseResponse>> getProjectPhasesByProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String proposalId) {
        List<ProjectPhaseResponse> phases = projectPhaseService.getProjectPhasesByProposal(proposalId);
        return ResponseEntity.ok(phases);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a project phase",
        description = "Permanently deletes a project phase"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Project phase deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Project phase not found")
    })
    public ResponseEntity<Void> deleteProjectPhase(
            @Parameter(description = "Project phase ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        projectPhaseService.deleteProjectPhase(id);
        return ResponseEntity.noContent().build();
    }
}
