package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@Schema(description = "Response object containing project phase information")
public class ProjectPhaseResponse {
    
    @Schema(description = "Unique project phase identifier", example = "uuid-here")
    private String id;
    
    @Schema(description = "Phase name", example = "Planning & Design")
    private String name;
    
    @Schema(description = "Duration in weeks", example = "4")
    private Integer durationWeeks;
    
    @Schema(description = "Cost for this phase", example = "15000.00")
    private BigDecimal cost;
    
    @Schema(description = "Timestamp when the phase was created", example = "2024-01-15T10:30:00")
    private Timestamp createdDateTime;
    
    @Schema(description = "Timestamp when the phase was last updated", example = "2024-01-15T15:45:00")
    private Timestamp lastUpdatedDateTime;
}
