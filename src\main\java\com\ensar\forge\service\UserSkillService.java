package com.ensar.forge.service;

import com.ensar.forge.dto.UserSkillRequest;
import com.ensar.forge.dto.UserSkillResponse;
import com.ensar.forge.entity.Skill;
import com.ensar.forge.entity.User;
import com.ensar.forge.entity.UserSkill;
import com.ensar.forge.repository.SkillRepository;
import com.ensar.forge.repository.UserRepository;
import com.ensar.forge.repository.UserSkillRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class UserSkillService {

    private final UserSkillRepository userSkillRepository;
    private final UserRepository userRepository;
    private final SkillRepository skillRepository;

    public UserSkillResponse addSkillToUser(String userId, UserSkillRequest request) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        Skill skill = skillRepository.findById(request.getSkillId())
                .orElseThrow(() -> new RuntimeException("Skill not found with id: " + request.getSkillId()));

        if (userSkillRepository.existsByUserIdAndSkillId(userId, request.getSkillId())) {
            throw new RuntimeException("User already has this skill");
        }

        UserSkill userSkill = new UserSkill();
        userSkill.setUser(user);
        userSkill.setSkill(skill);
        userSkill.setProficiencyLevel(request.getProficiencyLevel());

        UserSkill savedUserSkill = userSkillRepository.save(userSkill);
        return mapToResponse(savedUserSkill);
    }

    public UserSkillResponse updateUserSkill(String id, UserSkillRequest request) {
        UserSkill userSkill = userSkillRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User skill not found with id: " + id));

        userSkill.setProficiencyLevel(request.getProficiencyLevel());
        UserSkill updatedUserSkill = userSkillRepository.save(userSkill);
        return mapToResponse(updatedUserSkill);
    }

    public List<UserSkillResponse> getUserSkills(String userId) {
        List<UserSkill> userSkills = userSkillRepository.findByUserIdWithSkill(userId);
        return userSkills.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void removeSkillFromUser(String userId, String skillId) {
        if (!userSkillRepository.existsByUserIdAndSkillId(userId, skillId)) {
            throw new RuntimeException("User skill not found");
        }
        userSkillRepository.deleteByUserIdAndSkillId(userId, skillId);
    }

    public List<UserSkillResponse> getUsersBySkill(String skillId) {
        List<UserSkill> userSkills = userSkillRepository.findBySkillId(skillId);
        return userSkills.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public UserSkillResponse getUserSkillById(String id) {
        UserSkill userSkill = userSkillRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User skill not found with id: " + id));
        return mapToResponse(userSkill);
    }

    private UserSkillResponse mapToResponse(UserSkill userSkill) {
        UserSkillResponse response = new UserSkillResponse();
        response.setId(userSkill.getId());
        response.setUserId(userSkill.getUser().getId());
        response.setSkillId(userSkill.getSkill().getId());
        response.setSkill(userSkill.getSkill());
        response.setSkillName(userSkill.getSkill().getName());
        response.setSkillDescription(userSkill.getSkill().getDescription());
        response.setProficiencyLevel(userSkill.getProficiencyLevel().name());
        response.setCreatedDateTime(userSkill.getCreatedDateTime());
        response.setLastUpdatedDateTime(userSkill.getLastUpdatedDateTime());
        return response;
    }
}
