package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

@Data
@Schema(description = "Response object containing proposal attachment information")
public class ProposalAttachmentResponse {
    
    @Schema(description = "Unique attachment identifier", example = "uuid-here")
    private String id;
    
    @Schema(description = "File name", example = "proposal_document.pdf")
    private String fileName;
    
    @Schema(description = "File type/MIME type", example = "application/pdf")
    private String fileType;
    
    @Schema(description = "File size in bytes", example = "1024000")
    private Long fileSize;
    
    @Schema(description = "Timestamp when the attachment was created", example = "2024-01-15T10:30:00")
    private Timestamp createdDateTime;
    
    @Schema(description = "Timestamp when the attachment was last updated", example = "2024-01-15T15:45:00")
    private Timestamp lastUpdatedDateTime;
}
