-- Create projects table
CREATE TABLE projects (
    id CHAR(36) PRIMARY KEY,   -- UUID
    title VARCHAR(255) NOT NULL,
    description TEXT,
    user_id CHAR(36) NULL,
    priority ENUM('LOW', 'MEDIUM', 'HIGH') NOT NULL DEFAULT 'MEDIUM',
    status ENUM('ACTIVE', 'IN_PROGRESS', 'PENDING', 'COMPLETED') NOT NULL DEFAULT 'ACTIVE',
    min_budget DECIMAL(15,2) NOT NULL,
    max_budget DECIMAL(15,2) NULL,
    location VARCHAR(100),
    deadline DATE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create project_skills table
CREATE TABLE project_skills (
    id CHAR(36) PRIMARY KEY,    -- UUID for join table
    project_id CHAR(36) NOT NULL,
    skill_id CHAR(36) NOT NULL,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    UNIQUE (project_id, skill_id)   -- prevent duplicates
);

-- Create indexes for better performance
CREATE INDEX idx_projects_title ON projects(title);
CREATE INDEX idx_projects_priority ON projects(priority);
CREATE INDEX idx_projects_location ON projects(location);
CREATE INDEX idx_projects_deadline ON projects(deadline);
CREATE INDEX idx_project_skills_project_id ON project_skills(project_id);
CREATE INDEX idx_project_skills_skill_id ON project_skills(skill_id);
