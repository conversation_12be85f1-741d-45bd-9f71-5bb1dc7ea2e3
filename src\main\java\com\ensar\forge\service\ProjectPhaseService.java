package com.ensar.forge.service;

import com.ensar.forge.dto.ProjectPhaseRequest;
import com.ensar.forge.dto.ProjectPhaseResponse;
import com.ensar.forge.entity.ProjectPhase;
import com.ensar.forge.entity.Proposal;
import com.ensar.forge.repository.ProjectPhaseRepository;
import com.ensar.forge.repository.ProposalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProjectPhaseService {

    private final ProjectPhaseRepository projectPhaseRepository;
    private final ProposalRepository proposalRepository;

    public ProjectPhaseResponse createProjectPhase(String proposalId, ProjectPhaseRequest request) {
        Proposal proposal = proposalRepository.findById(proposalId)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + proposalId));

        ProjectPhase phase = new ProjectPhase();
        phase.setProposal(proposal);
        phase.setName(request.getName());
        phase.setDurationWeeks(request.getDurationWeeks());
        phase.setCost(request.getCost());

        ProjectPhase savedPhase = projectPhaseRepository.save(phase);
        return mapToResponse(savedPhase);
    }

    public ProjectPhaseResponse updateProjectPhase(String id, ProjectPhaseRequest request) {
        ProjectPhase phase = projectPhaseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Project phase not found with id: " + id));

        phase.setName(request.getName());
        phase.setDurationWeeks(request.getDurationWeeks());
        phase.setCost(request.getCost());

        ProjectPhase updatedPhase = projectPhaseRepository.save(phase);
        return mapToResponse(updatedPhase);
    }

    public ProjectPhaseResponse getProjectPhase(String id) {
        ProjectPhase phase = projectPhaseRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Project phase not found with id: " + id));
        return mapToResponse(phase);
    }

    public List<ProjectPhaseResponse> getProjectPhasesByProposal(String proposalId) {
        return projectPhaseRepository.findByProposalIdOrderByCreatedDateTime(proposalId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void deleteProjectPhase(String id) {
        if (!projectPhaseRepository.existsById(id)) {
            throw new RuntimeException("Project phase not found with id: " + id);
        }
        projectPhaseRepository.deleteById(id);
    }

    public void deleteProjectPhasesByProposal(String proposalId) {
        projectPhaseRepository.deleteByProposalId(proposalId);
    }

    private ProjectPhaseResponse mapToResponse(ProjectPhase phase) {
        ProjectPhaseResponse response = new ProjectPhaseResponse();
        response.setId(phase.getId());
        response.setName(phase.getName());
        response.setDurationWeeks(phase.getDurationWeeks());
        response.setCost(phase.getCost());
        response.setCreatedDateTime(phase.getCreatedDateTime());
        response.setLastUpdatedDateTime(phase.getLastUpdatedDateTime());
        return response;
    }
}
