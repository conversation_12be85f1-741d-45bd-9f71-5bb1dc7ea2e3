package com.ensar.forge.repository;

import com.ensar.forge.entity.Project;
import com.ensar.forge.entity.Project.Priority;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectRepository extends JpaRepository<Project, String> {
    
    List<Project> findByPriority(Priority priority);
    
    List<Project> findByLocationContainingIgnoreCase(String location);

    @Query("""
       SELECT DISTINCT p
       FROM Project p
       LEFT JOIN FETCH p.projectSkills ps
       LEFT JOIN FETCH ps.skill
       WHERE p.id = :id
       """)
    Optional<Project> findByIdWithSkills(@Param("id") String id);
    
    boolean existsByTitle(String title);

    @Query("""
    SELECT p FROM Project p
    WHERE (:searchTerm IS NULL OR 
           LOWER(p.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR 
           LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')))
      AND (:priorities IS NULL OR p.priority IN :priorities)
      AND (:minBudget IS NULL OR p.minBudget >= :minBudget)
      AND (:maxBudget IS NULL OR p.maxBudget <= :maxBudget)
      AND (:locations IS NULL OR p.location IN :locations)
      AND (:deadlines IS NULL OR p.deadline IN :deadlines)
      AND (:currentUserId IS NULL OR p.user.id = :currentUserId)
      AND (:statuses IS NULL OR p.status IN :statuses)
""")
    Page<Project> findAllWithPaginationAndFilters(
            @Param("searchTerm") String searchTerm,
            @Param("priorities") List<Project.Priority> priorities,
            @Param("minBudget") java.math.BigDecimal minBudget,
            @Param("maxBudget") java.math.BigDecimal maxBudget,
            @Param("locations") List<String> locations,
            @Param("deadlines") List<Date> deadlines,
            @Param("currentUserId") String currentUserId,
            @Param("statuses") List<Project.Status> statuses,
            Pageable pageable);
    
    @Query("SELECT DISTINCT p.location FROM Project p WHERE p.location IS NOT NULL ORDER BY p.location")
    List<String> findAllDistinctLocations();
    
    List<Project> findByUserId(String userId);
    
    long countByStatus(Project.Status status);
}
