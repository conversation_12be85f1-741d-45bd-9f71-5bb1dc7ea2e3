package com.ensar.forge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
@Schema(description = "Request object for creating a proposal attachment")
public class ProposalAttachmentRequest {
    
    @Schema(description = "File to upload", required = true)
    @NotNull(message = "File is required")
    private MultipartFile file;
    
    @Schema(description = "Proposal ID to attach the file to", example = "uuid-here", required = true)
    @NotBlank(message = "Proposal ID is required")
    private String proposalId;
}
