package com.ensar.forge.service;

import com.ensar.forge.dto.CompanyRequest;
import com.ensar.forge.dto.CompanyResponse;
import com.ensar.forge.entity.Company;
import com.ensar.forge.repository.CompanyRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class CompanyService {

    private final CompanyRepository companyRepository;

    public CompanyResponse createCompany(CompanyRequest request) {
        if (companyRepository.existsByName(request.getName())) {
            throw new RuntimeException("Company with name '" + request.getName() + "' already exists");
        }
        
        if (request.getDomain() != null && companyRepository.existsByDomain(request.getDomain())) {
            throw new RuntimeException("Company with domain '" + request.getDomain() + "' already exists");
        }

        Company company = new Company();
        company.setName(request.getName());
        company.setDescription(request.getDescription());
        company.setDomain(request.getDomain());
        company.setDisabled(request.getDisabled() != null ? request.getDisabled() : false);

        Company savedCompany = companyRepository.save(company);
        return mapToResponse(savedCompany);
    }

    public CompanyResponse updateCompany(String id, CompanyRequest request) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Company not found with id: " + id));

        if (!company.getName().equals(request.getName()) && companyRepository.existsByName(request.getName())) {
            throw new RuntimeException("Company with name '" + request.getName() + "' already exists");
        }

        if (request.getDomain() != null && !request.getDomain().equals(company.getDomain()) && 
            companyRepository.existsByDomain(request.getDomain())) {
            throw new RuntimeException("Company with domain '" + request.getDomain() + "' already exists");
        }

        company.setName(request.getName());
        company.setDescription(request.getDescription());
        company.setDomain(request.getDomain());
        if (request.getDisabled() != null) {
            company.setDisabled(request.getDisabled());
        }

        Company updatedCompany = companyRepository.save(company);
        return mapToResponse(updatedCompany);
    }

    public CompanyResponse getCompany(String id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Company not found with id: " + id));
        return mapToResponse(company);
    }

    public List<CompanyResponse> getAllCompanies() {
        return companyRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<CompanyResponse> getActiveCompanies() {
        return companyRepository.findByDisabled(false).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void deleteCompany(String id) {
        if (!companyRepository.existsById(id)) {
            throw new RuntimeException("Company not found with id: " + id);
        }
        companyRepository.deleteById(id);
    }

    private CompanyResponse mapToResponse(Company company) {
        CompanyResponse response = new CompanyResponse();
        response.setId(company.getId());
        response.setName(company.getName());
        response.setDescription(company.getDescription());
        response.setDomain(company.getDomain());
        response.setDisabled(company.getDisabled());
        response.setCreatedDateTime(company.getCreatedDateTime());
        response.setLastUpdatedDateTime(company.getLastUpdatedDateTime());
        return response;
    }
}


