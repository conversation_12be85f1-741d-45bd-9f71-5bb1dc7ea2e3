CREATE TABLE companies (
    id VARCHAR(36) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    description VARCHAR(500) NULL,
    domain VARCHAR(50) NULL,
    disabled BOOLEAN NOT NULL DEFAULT FALSE,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX idx_companies_name ON companies(name);
CREATE INDEX idx_companies_domain ON companies(domain);
CREATE INDEX idx_companies_disabled ON companies(disabled);

INSERT INTO companies (id, name, description, domain, disabled, created_date_time, last_updated_date_time)
VALUES
('4e5d1b6e-0f7b-4a0f-9b30-f2f48e73d56b', 'TechCorp Solutions', 'Leading technology solutions provider', 'techcorp.com', FALSE, NOW(), NULL),
('0b7f36a2-5f11-4cd5-89ad-9b0c4a6129b0', 'InnovateSoft', 'Software innovation company', 'innovatesoft.com', FALSE, NOW(), NULL),
('d21f6dbf-dff8-4be2-8e42-771d515151ae', 'DataFlow Systems', 'Data management and analytics', 'dataflow.com', FALSE, NOW(), NULL),
('62de8986-7cf9-4c94-9d93-67c4f9fc471c', 'CloudTech Pro', 'Cloud infrastructure services', 'cloudtechpro.com', FALSE, NOW(), NULL);


CREATE TABLE users (
    id VARCHAR(36) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(120),
    role ENUM('REQUESTING_SI', 'PROVIDING_SI', 'ADMIN', 'SUPER_ADMIN') NOT NULL,
    account_expiry_date DATE,
    account_non_expired BOOLEAN NOT NULL DEFAULT TRUE,
    account_non_locked BOOLEAN NOT NULL DEFAULT TRUE,
    credentials_expiry_date DATE,
    credentials_non_expired BOOLEAN NOT NULL DEFAULT TRUE,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    is_two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    sign_up_method VARCHAR(255),
    two_factor_secret VARCHAR(255),
    company_id VARCHAR(36) NULL,
    created_date_time    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
);

-- Create index on email for faster lookups
CREATE INDEX idx_users_email ON users(email);

-- Create index on role for role-based queries
CREATE INDEX idx_users_role ON users(role);

CREATE INDEX idx_users_company_id ON users(company_id);

-- Create index on created_date_time for time-based queries
CREATE INDEX idx_users_created_date_time ON users(created_date_time);


INSERT INTO users (id, first_name, last_name, email, password, role, account_expiry_date, account_non_expired, account_non_locked, credentials_expiry_date, credentials_non_expired, enabled, is_two_factor_enabled, sign_up_method, two_factor_secret, company_id, created_date_time, last_updated_date_time)
VALUES
('b905c640-8db3-4457-8dbf-3cc6d2eeb637', 'John', 'Doe', '<EMAIL>', '$2a$10$qt8Sg/sMOwkSLil9g9UMM.h.5V7KFRn99PQgiQWPR.QINl3RWu4QG', 'REQUESTING_SI', NULL, TRUE, TRUE, NULL, TRUE, TRUE, FALSE, 'EMAIL', NULL, '4e5d1b6e-0f7b-4a0f-9b30-f2f48e73d56b',NOW(), NULL),
('16f9911c-8261-472a-a6c0-bd0c6a226682', 'Jane', 'Smith', '<EMAIL>', '$2a$10$qt8Sg/sMOwkSLil9g9UMM.h.5V7KFRn99PQgiQWPR.QINl3RWu4QG', 'PROVIDING_SI', NULL, TRUE, TRUE, NULL, TRUE, TRUE, FALSE, 'EMAIL', NULL,'4e5d1b6e-0f7b-4a0f-9b30-f2f48e73d56b', NOW(), NULL),
('3437610b-d580-490f-a9d6-90a978b24bb8', 'Alice', 'Johnson', '<EMAIL>', '$2a$10$qt8Sg/sMOwkSLil9g9UMM.h.5V7KFRn99PQgiQWPR.QINl3RWu4QG', 'ADMIN', NULL, TRUE, TRUE, NULL, TRUE, TRUE, TRUE, 'EMAIL', NULL,'4e5d1b6e-0f7b-4a0f-9b30-f2f48e73d56b', NOW(), NULL),
('f2a6fd8e-2b26-4307-8570-2d95d9b9eae2', 'Bob', 'Brown', '<EMAIL>', '$2a$10$qt8Sg/sMOwkSLil9g9UMM.h.5V7KFRn99PQgiQWPR.QINl3RWu4QG', 'SUPER_ADMIN', NULL, TRUE, TRUE, NULL, TRUE, TRUE, TRUE, 'EMAIL', NULL, '62de8986-7cf9-4c94-9d93-67c4f9fc471c', NOW(), NULL);

CREATE TABLE skills (
    id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id)
);

CREATE INDEX idx_skills_name ON skills(name);

INSERT INTO skills (id, name, description, created_date_time, last_updated_date_time)
VALUES
('34eb54c3-bd55-45fa-9968-7b91b6ad15c5', 'Java', 'Java programming language', NOW(), NULL),
('084b7177-13c1-4707-bf7c-0d89f56d3652', 'Python', 'Python programming language', NOW(), NULL),
('f15789d6-9e41-43aa-a019-f5c0036b2bd8', 'JavaScript', 'JavaScript programming language', NOW(), NULL),
('97993a1f-37b1-4dd1-8c41-b3ab43c27ad6', 'Spring Boot', 'Spring Boot framework', NOW(), NULL),
('b90cfc11-dad7-41f6-b62e-693fbdb5b2c9', 'React', 'React frontend framework', NOW(), NULL),
('5ad8ed59-6b55-498e-9271-7a5a2bb2c3a7', 'Docker', 'Containerization technology', NOW(), NULL),
('e4a7fbd6-8590-4a6d-8654-6e6bcf26bbd2', 'Kubernetes', 'Container orchestration', NOW(), NULL),
('1f6c3e68-3a28-4f1e-9f27-1a6d2ef8907d', 'AWS', 'Amazon Web Services', NOW(), NULL),
('e2adcf35-bc5f-4cd7-a7cc-f4bb21e52d1a', 'SQL', 'Structured Query Language', NOW(), NULL),
('8df8d43d-7d06-4c00-b51b-5f06e3cf0039', 'Git', 'Version control system', NOW(), NULL);


CREATE TABLE user_skills (
    id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    skill_id VARCHAR(36) NOT NULL,
    proficiency_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') NOT NULL,
    created_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    UNIQUE (user_id, skill_id)
);

CREATE INDEX idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX idx_user_skills_skill_id ON user_skills(skill_id);

INSERT INTO user_skills (id, user_id, skill_id, proficiency_level, created_date_time, last_updated_date_time)
SELECT
    UUID(),
    u.id,
    s.id,
    'ADVANCED',
    NOW(),
    NULL
FROM users u, skills s
WHERE u.email = '<EMAIL>' AND s.name IN ('Java', 'Spring Boot', 'SQL');

INSERT INTO user_skills (id, user_id, skill_id, proficiency_level, created_date_time, last_updated_date_time)
SELECT
    UUID(),
    u.id,
    s.id,
    'INTERMEDIATE',
    NOW(),
    NULL
FROM users u, skills s
WHERE u.email = '<EMAIL>' AND s.name IN ('Python', 'React', 'Git');

INSERT INTO user_skills (id, user_id, skill_id, proficiency_level, created_date_time, last_updated_date_time)
SELECT
    UUID(),
    u.id,
    s.id,
    'ADVANCED',
    NOW(),
    NULL
FROM users u, skills s
WHERE u.email = '<EMAIL>' AND s.name IN ('Java', 'Python', 'Docker', 'Kubernetes');

INSERT INTO user_skills (id, user_id, skill_id, proficiency_level, created_date_time, last_updated_date_time)
SELECT
    UUID(),
    u.id,
    s.id,
    'ADVANCED',
    NOW(),
    NULL
FROM users u, skills s
WHERE u.email = '<EMAIL>' AND s.name IN ('AWS', 'Docker', 'Kubernetes', 'Git');
