package com.ensar.forge.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "project_phases")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProjectPhase extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "proposal_id", nullable = false)
    private Proposal proposal;

    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Column(name = "duration_weeks")
    private Integer durationWeeks;

    @Column(name = "cost", precision = 15, scale = 2)
    private java.math.BigDecimal cost;

    @Column(name = "is_deleted")
    private boolean isDeleted = false;
}
