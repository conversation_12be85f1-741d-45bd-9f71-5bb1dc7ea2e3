package com.ensar.forge.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;

@Entity
@Table(name = "users")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = {"userSkills"})
public class User extends BaseEntity {

    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Column(name = "last_name", nullable = false)
    private String lastName;
    
    @Column( name="email", unique = true, nullable = false, length = 50)
    private String email;
    
    @Column(name = "password",length = 120)
    @JsonIgnore
    private String password;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Role role;
    
    @Column(name = "account_expiry_date")
    private LocalDate accountExpiryDate;
    
    @Column(name = "account_non_expired", nullable = false)
    private Boolean accountNonExpired;
    
    @Column(name = "account_non_locked", nullable = false)
    private Boolean accountNonLocked;
    
    @Column(name = "credentials_expiry_date")
    private LocalDate credentialsExpiryDate;
    
    @Column(name = "credentials_non_expired", nullable = false)
    private Boolean credentialsNonExpired;
    
    @Column(nullable = false)
    private Boolean enabled;
    
    @Column(name = "is_two_factor_enabled", nullable = false)
    private Boolean isTwoFactorEnabled;
    
    @Column(name = "sign_up_method", length = 255)
    private String signUpMethod;
    
    @Column(name = "two_factor_secret", length = 255)
    private String twoFactorSecret;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnore
    private java.util.Set<UserSkill> userSkills = new LinkedHashSet<>();

    public enum Role {
        REQUESTING_SI,
        PROVIDING_SI,
        ADMIN,
        SUPER_ADMIN
    }

}
