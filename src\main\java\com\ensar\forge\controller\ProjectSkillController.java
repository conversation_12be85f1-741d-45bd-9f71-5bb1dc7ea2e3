package com.ensar.forge.controller;

import com.ensar.forge.dto.ProjectSkillRequest;
import com.ensar.forge.dto.ProjectSkillResponse;
import com.ensar.forge.service.ProjectSkillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/project-skills")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Project-Skill Management", description = "APIs for managing the relationship between projects and skills")
public class ProjectSkillController {

    private final ProjectSkillService projectSkillService;

    @PostMapping
    @Operation(
        summary = "Add a skill to a project",
        description = "Creates a new association between a project and a skill"
    )
    public ResponseEntity<ProjectSkillResponse> addSkillToProject(
            @Parameter(description = "Project-skill association details", required = true)
            @Valid @RequestBody ProjectSkillRequest request) {
        ProjectSkillResponse response = projectSkillService.addSkillToProject(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get project-skill association by ID",
        description = "Retrieves a specific project-skill association with full details"
    )
    public ResponseEntity<ProjectSkillResponse> getProjectSkill(
            @Parameter(description = "Project-skill association ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProjectSkillResponse response = projectSkillService.getProjectSkill(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all project-skill associations",
        description = "Retrieves a list of all project-skill associations"
    )
    public ResponseEntity<List<ProjectSkillResponse>> getAllProjectSkills() {
        List<ProjectSkillResponse> projectSkills = projectSkillService.getAllProjectSkills();
        return ResponseEntity.ok(projectSkills);
    }

    @GetMapping("/project/{projectId}")
    @Operation(
        summary = "Get all skills for a specific project",
        description = "Retrieves all skills associated with a particular project"
    )
    public ResponseEntity<List<ProjectSkillResponse>> getSkillsByProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String projectId) {
        List<ProjectSkillResponse> projectSkills = projectSkillService.getSkillsByProject(projectId);
        return ResponseEntity.ok(projectSkills);
    }

    @GetMapping("/skill/{skillId}")
    @Operation(
        summary = "Get all projects for a specific skill",
        description = "Retrieves all projects that require a particular skill"
    )
    public ResponseEntity<List<ProjectSkillResponse>> getProjectsBySkill(
            @Parameter(description = "Skill ID", required = true, example = "uuid-here")
            @PathVariable String skillId) {
        List<ProjectSkillResponse> projectSkills = projectSkillService.getProjectsBySkill(skillId);
        return ResponseEntity.ok(projectSkills);
    }

    @DeleteMapping("/project/{projectId}/skill/{skillId}")
    @Operation(
        summary = "Remove a skill from a project",
        description = "Removes the association between a specific skill and project"
    )
    public ResponseEntity<Void> removeSkillFromProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String projectId, 
            @Parameter(description = "Skill ID", required = true, example = "uuid-here")
            @PathVariable String skillId) {
        projectSkillService.removeSkillFromProject(projectId, skillId);
        return ResponseEntity.noContent().build();
    }
}
