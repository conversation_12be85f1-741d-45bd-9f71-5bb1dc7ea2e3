package com.ensar.forge.dto;

import com.ensar.forge.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "User registration request containing user details")
public class UserRegistrationRequest {

    @Schema(description = "User's first name", required = true)
    private String firstName;

    @Schema(description = "User's last name", required = true)
    private String lastName;

    @Schema(description = "User's email address", example = "<EMAIL>", required = true)
    private String email;
    
    @Schema(description = "User's password (minimum 8 characters)", example = "password123", required = true)
    private String password;
    
    @Schema(description = "User's role in the system", example = "REQUESTING_SI", defaultValue = "REQUESTING_SI")
    private User.Role role;
    
    @Schema(description = "Method used for user registration", example = "EMAIL", allowableValues = {"EMAIL", "GOOGLE", "FACEBOOK"})
    private String signUpMethod;
    
    @Schema(description = "Company ID for the user", example = "uuid-string")
    private String companyId;
}
