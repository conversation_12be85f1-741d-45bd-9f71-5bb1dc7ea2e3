package com.ensar.forge.service;

import com.ensar.forge.dto.UserCompanyResponse;
import com.ensar.forge.dto.UserResponse;
import com.ensar.forge.dto.UserSkillResponse;
import com.ensar.forge.entity.User;
import com.ensar.forge.exception.ResourceNotFoundException;
import com.ensar.forge.repository.UserRepository;
import com.ensar.forge.service.UserSkillService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserSkillService userSkillService;

    public UserResponse getUserById(String id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
        return mapToResponse(user);
    }

    public UserResponse getUserByEmail(String email) {
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
        return mapToResponse(user);
    }

    public User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
    }


    public List<UserResponse> getAllUsers() {
        return userRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<UserResponse> getUsersByCompany(String companyId) {
        return userRepository.findByCompanyId(companyId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<UserResponse> getUsersByRole(User.Role role) {
        return userRepository.findByRole(role).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public UserResponse mapToResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setFirstName(user.getFirstName());
        response.setLastName(user.getLastName());
        response.setEmail(user.getEmail());
        response.setRole(user.getRole());
        response.setAccountExpiryDate(user.getAccountExpiryDate());
        response.setAccountNonExpired(user.getAccountNonExpired());
        response.setAccountNonLocked(user.getAccountNonLocked());
        response.setCredentialsExpiryDate(user.getCredentialsExpiryDate());
        response.setCredentialsNonExpired(user.getCredentialsNonExpired());
        response.setEnabled(user.getEnabled());
        response.setIsTwoFactorEnabled(user.getIsTwoFactorEnabled());
        response.setSignUpMethod(user.getSignUpMethod());
        response.setTwoFactorSecret(user.getTwoFactorSecret());
        response.setCreatedDateTime(user.getCreatedDateTime());
        response.setLastUpdatedDateTime(user.getLastUpdatedDateTime());

        // Map company information
        if (user.getCompany() != null) {
            UserCompanyResponse companyResponse = new UserCompanyResponse();
            companyResponse.setId(user.getCompany().getId());
            companyResponse.setName(user.getCompany().getName());
            companyResponse.setDescription(user.getCompany().getDescription());
            companyResponse.setDomain(user.getCompany().getDomain());
            companyResponse.setDisabled(user.getCompany().getDisabled());
            response.setCompany(companyResponse);
        }

        // Map skills information
        List<UserSkillResponse> skills = userSkillService.getUserSkills(user.getId());
        response.setSkills(skills);

        return response;
    }
}
