package com.ensar.forge.controller;

import com.ensar.forge.dto.ProjectRequest;
import com.ensar.forge.dto.ProjectResponse;
import com.ensar.forge.entity.Project.Priority;
import com.ensar.forge.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/projects")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Project Management", description = "APIs for managing projects and their skills")
public class ProjectController {

    private final ProjectService projectService;

    @PostMapping
    @Operation(
        summary = "Create a new project",
        description = "Creates a new project with the provided details and optionally associates skills"
    )
    public ResponseEntity<ProjectResponse> createProject(
            @Parameter(description = "Project details", required = true)
            @Valid @RequestBody ProjectRequest request) {
        ProjectResponse response = projectService.createProject(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update an existing project",
        description = "Updates a project with the provided details and optionally updates associated skills"
    )
    public ResponseEntity<ProjectResponse> updateProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String id, 
            @Parameter(description = "Updated project details", required = true)
            @Valid @RequestBody ProjectRequest request) {
        ProjectResponse response = projectService.updateProject(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get project by ID",
        description = "Retrieves a specific project with all its details and associated skills"
    )
    public ResponseEntity<ProjectResponse> getProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProjectResponse response = projectService.getProject(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    @Operation(        summary = "Get all projects",
            description = "Retrieves a list of all projects with their basic information")
    public ResponseEntity<List<ProjectResponse>> getAllProjects() {
        List<ProjectResponse> projects = projectService.getAllProjects();
        return ResponseEntity.ok(projects);
    }

    @GetMapping
    @Operation(
        summary = "Get all projects with pagination and filters",
        description = "Retrieves a paginated list of projects with optional filtering by title, budget ranges, location, deadline, and priority"
    )
    @ApiResponse(responseCode = "200", description = "Projects retrieved successfully")
    public ResponseEntity<Page<ProjectResponse>> getAllProjects(
            @Parameter(description = "Page number", required = false)
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", required = false)
            @RequestParam(defaultValue = "25") int size,
            @Parameter(description = "Search term for title or description", required = false)
            @RequestParam Optional<String> searchTerm,
            @Parameter(description = "Priority filter (LOW, MEDIUM, HIGH)", required = false)
            @RequestParam Optional<String> priority,
            @Parameter(description = "Budget range filter (e.g., 'Under $10K', '$10K - $25K', 'Over $100K')", required = false)
            @RequestParam Optional<String> budget,
            @Parameter(description = "Location filter", required = false)
            @RequestParam Optional<String> location,
            @Parameter(description = "Deadline filter (YYYY-MM-DD format)", required = false)
            @RequestParam Optional<String> deadline) {
        Page<ProjectResponse> projects = projectService.getAllProjectsWithPagination(page, size, searchTerm, priority, budget, location, deadline);
        return ResponseEntity.ok(projects);
    }

    @GetMapping("/locations")
    @Operation(
        summary = "Get all unique locations",
        description = "Retrieves a list of all unique locations from projects table"
    )
    public ResponseEntity<List<String>> getAllLocations() {
        List<String> locations = projectService.getAllLocations();
        return ResponseEntity.ok(locations);
    }

    @GetMapping("/location")
    @Operation(
        summary = "Get projects by location",
        description = "Retrieves all projects in the specified location"
    )
    public ResponseEntity<List<ProjectResponse>> getProjectsByLocation(
            @Parameter(description = "Location to search for", required = true, example = "Remote")
            @RequestParam String location) {
        List<ProjectResponse> projects = projectService.getProjectsByLocation(location);
        return ResponseEntity.ok(projects);
    }

    @PostMapping("/{projectId}/skills")
    @Operation(
        summary = "Add skills to a project",
        description = "Associates multiple skills with a specific project"
    )
    public ResponseEntity<Void> addSkillsToProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String projectId, 
            @Parameter(description = "List of skill IDs to add", required = true)
            @RequestBody List<String> skillIds) {
        projectService.addSkillsToProject(projectId, skillIds);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{projectId}/skills/{skillId}")
    @Operation(
        summary = "Remove a skill from a project",
        description = "Removes the association between a specific skill and project"
    )
    public ResponseEntity<Void> removeSkillFromProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String projectId, 
            @Parameter(description = "Skill ID to remove", required = true, example = "uuid-here")
            @PathVariable String skillId) {
        projectService.removeSkillFromProject(projectId, skillId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/user/{userId}")
    @Operation(
        summary = "Get projects by user",
        description = "Retrieves all projects created by a specific user"
    )
    public ResponseEntity<List<ProjectResponse>> getProjectsByUser(
            @Parameter(description = "User ID", required = true, example = "uuid-here")
            @PathVariable String userId) {
        List<ProjectResponse> projects = projectService.getProjectsByUser(userId);
        return ResponseEntity.ok(projects);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a project",
        description = "Permanently deletes a project and all its associated skills"
    )
    public ResponseEntity<Void> deleteProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        projectService.deleteProject(id);
        return ResponseEntity.noContent().build();
    }
}
