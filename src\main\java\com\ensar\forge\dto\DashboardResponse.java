package com.ensar.forge.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardResponse {
    
    // For REQUESTING_SI (Project Owners/Clients)
    private Long activeProjects;
    private Long totalProposals;
    private Long completedProjects;
    
    // For PROVIDING_SI (Service Providers)
    private Long activeProposals;
    private Long wonProjects;
    private Long profileViews;
    
    // Constructor for REQUESTING_SI metrics
    public DashboardResponse(long activeProjects, long totalProposals, long completedProjects) {
        this.activeProjects = activeProjects;
        this.totalProposals = totalProposals;
        this.completedProjects = completedProjects;
        this.activeProposals = null;
        this.wonProjects = null;
        this.profileViews = null;
    }
    
    // Constructor for PROVIDING_SI metrics
    public static DashboardResponse forProvidingSI(long activeProposals, long wonProjects, long profileViews) {
        DashboardResponse response = new DashboardResponse();
        response.activeProposals = activeProposals;
        response.wonProjects = wonProjects;
        response.profileViews = profileViews;
        response.activeProjects = null;
        response.totalProposals = null;
        response.completedProjects = null;
        return response;
    }
}
