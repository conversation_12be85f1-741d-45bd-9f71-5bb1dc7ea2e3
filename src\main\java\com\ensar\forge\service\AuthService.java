//package com.ensar.forge.service;
//
//import com.ensar.forge.dto.AuthRequest;
//import com.ensar.forge.dto.AuthResponse;
//import com.ensar.forge.dto.UserRegistrationRequest;
//import com.ensar.forge.entity.Company;
//import com.ensar.forge.entity.User;
//import com.ensar.forge.repository.CompanyRepository;
//import com.ensar.forge.repository.UserRepository;
//import com.ensar.forge.security.JwtUtil;
//import lombok.RequiredArgsConstructor;
//import org.springframework.security.authentication.AuthenticationManager;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.stereotype.Service;
//
//import java.util.UUID;
//
//@Service
//@RequiredArgsConstructor
//public class AuthService {
//
//    private final UserRepository userRepository;
//    private final CompanyRepository companyRepository;
//    private final PasswordEncoder passwordEncoder;
//    private final JwtUtil jwtUtil;
//    private final AuthenticationManager authenticationManager;
//
//    public AuthResponse register(UserRegistrationRequest request) {
//        if (userRepository.existsByEmail(request.getEmail())) {
//            throw new RuntimeException("User with this email already exists");
//        }
//
//        User user = new User();
//        user.setFirstName(request.getFirstName());
//        user.setLastName(request.getLastName());
//        user.setEmail(request.getEmail());
//        user.setPassword(passwordEncoder.encode(request.getPassword()));
//        user.setRole(request.getRole() != null ? request.getRole() : User.Role.REQUESTING_SI);
//        user.setAccountNonExpired(true);
//        user.setAccountNonLocked(true);
//        user.setCredentialsNonExpired(true);
//        user.setEnabled(true);
//        user.setIsTwoFactorEnabled(false);
//        user.setSignUpMethod("EMAIL");
//
//        // Set company if provided
//        if (request.getCompanyId() != null && !request.getCompanyId().trim().isEmpty()) {
//            Company company = companyRepository.findById(request.getCompanyId())
//                    .orElseThrow(() -> new RuntimeException("Company not found with id: " + request.getCompanyId()));
//            user.setCompany(company);
//        }
//
//        userRepository.save(user);
//
//        AuthResponse response = new AuthResponse();
//        response.setUser(user);
//        response.setMessage("User registered successfully");
//
//
//        return response;
//    }
//
//    public AuthResponse authenticate(AuthRequest request) {
//
//        Authentication authentication = authenticationManager.authenticate(
//                new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
//        );
//
//        User user = userRepository.findByEmail(request.getEmail())
//                .orElseThrow(() -> new RuntimeException("User not found"));
//
//        if (!user.getEnabled()) {
//            throw new RuntimeException("User account is disabled");
//        }
//
//        if (!user.getAccountNonLocked()) {
//            throw new RuntimeException("User account is locked");
//        }
//
//        if (!user.getAccountNonExpired()) {
//            throw new RuntimeException("User account has expired");
//        }
//
//        if (!user.getCredentialsNonExpired()) {
//            throw new RuntimeException("User credentials have expired");
//        }
//        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
//        String accessToken = jwtUtil.generateToken(userDetails);
//        String refreshToken = jwtUtil.generateRefreshToken(userDetails);
//
//        AuthResponse response = new AuthResponse();
//        response.setAccessToken(accessToken);
//        response.setUser(user);
//        response.setMessage("Authentication successful");
//
//        return response;
//    }
//
////    public AuthResponse refreshToken(String refreshToken) {
////        if (!jwtUtil.validateToken(refreshToken)) {
////            throw new RuntimeException("Invalid refresh token");
////        }
////
////        String email = jwtUtil.extractUsername(refreshToken);
////        User user = userRepository.findByEmail(email)
////                .orElseThrow(() -> new RuntimeException("User not found"));
////
////        String newAccessToken = jwtUtil.generateToken(user);
////        String newRefreshToken = jwtUtil.generateRefreshToken(user);
////
////        return AuthResponse.builder()
////                .accessToken(newAccessToken)
////                .refreshToken(newRefreshToken)
////                .email(user.getEmail())
////                .role(user.getRole())
////                .message("Token refreshed successfully")
////                .build();
////    }
//}



package com.ensar.forge.service;

import com.ensar.forge.dto.AuthRequest;
import com.ensar.forge.dto.AuthResponse;
import com.ensar.forge.dto.UserRegistrationRequest;
import com.ensar.forge.entity.Company;
import com.ensar.forge.entity.User;
import com.ensar.forge.exception.ResourceNotFoundException;
import com.ensar.forge.exception.ResourceAlreadyExistsException;
import com.ensar.forge.exception.UnauthorizedOperationException;
import com.ensar.forge.repository.CompanyRepository;
import com.ensar.forge.repository.UserRepository;
import com.ensar.forge.security.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final AuthenticationManager authenticationManager;
    private final UserService userService;

    public AuthResponse register(UserRegistrationRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ResourceAlreadyExistsException("User", "email", request.getEmail());
        }

        User user = new User();
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setRole(request.getRole() != null ? request.getRole() : User.Role.REQUESTING_SI);
        user.setAccountNonExpired(true);
        user.setAccountNonLocked(true);
        user.setCredentialsNonExpired(true);
        user.setEnabled(true);
        user.setIsTwoFactorEnabled(false);
        user.setSignUpMethod("EMAIL");

        // Set company if provided
        if (request.getCompanyId() != null && !request.getCompanyId().trim().isEmpty()) {
            Company company = companyRepository.findById(request.getCompanyId())
                    .orElseThrow(() -> new ResourceNotFoundException("Company", "id", request.getCompanyId()));
            user.setCompany(company);
        }

        User savedUser = userRepository.save(user);

        AuthResponse response = new AuthResponse();
        response.setUser(userService.mapToResponse(savedUser));
        response.setMessage("User registered successfully");

        return response;
    }

    public AuthResponse authenticate(AuthRequest request) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
        );

        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException("User", "email", request.getEmail()));

        if (!user.getEnabled()) {
            throw new UnauthorizedOperationException("User account is disabled");
        }

        if (!user.getAccountNonLocked()) {
            throw new UnauthorizedOperationException("User account is locked");
        }

        if (!user.getAccountNonExpired()) {
            throw new UnauthorizedOperationException("User account has expired");
        }

        if (!user.getCredentialsNonExpired()) {
            throw new UnauthorizedOperationException("User credentials have expired");
        }

        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String accessToken = jwtUtil.generateToken(userDetails);

        AuthResponse response = new AuthResponse();
        response.setAccessToken(accessToken);
        response.setUser(userService.mapToResponse(user));
        response.setMessage("Authentication successful");

        return response;
    }
}
