package com.ensar.forge.repository;

import com.ensar.forge.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {
    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);
    
    List<User> findByCompanyId(String companyId);
    List<User> findByRole(User.Role role);
    
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.userSkills us LEFT JOIN FETCH us.skill WHERE u.email = :email")
    Optional<User> findByEmailWithSkills(@Param("email") String email);
}
