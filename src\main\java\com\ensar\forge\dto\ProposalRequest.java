package com.ensar.forge.dto;

import com.ensar.forge.entity.Proposal.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "Request object for creating or updating a proposal")
public class ProposalRequest {
    
    @Schema(description = "Project ID for which the proposal is being submitted", example = "uuid-here", required = true)
    @NotBlank(message = "Project ID is required")
    private String projectId;
    
    @Schema(description = "Proposal status", example = "DRAFT", defaultValue = "DRAFT")
    private Status status = Status.PENDING;
    
    @Schema(description = "Total budget for the proposal", example = "50000.00", required = true)
    @NotNull(message = "Total budget is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Total budget must be greater than 0")
    private BigDecimal totalBudget;
    
    @Schema(description = "Timeline in weeks", example = "12", required = true)
    @NotNull(message = "Timeline weeks is required")
    @Min(value = 1, message = "Timeline must be at least 1 week")
    private Integer timelineWeeks;
    
    @Schema(description = "Detailed description of the proposal", example = "This proposal outlines a comprehensive solution...", required = true)
    @NotBlank(message = "Description is required")
    private String description;
    
    @Schema(description = "List of deliverables as JSON array", example = "[\"Web Application\", \"Mobile App\", \"Documentation\"]")
    private String deliverables;
    
    @Schema(description = "Approach and methodology", example = "We will use Agile methodology with weekly sprints...")
    private String approachMethodology;
    
    @Schema(description = "Relevant experience and qualifications", example = "Our team has 10+ years of experience...")
    private String relevantExperience;

    @Schema(description = "Rejection Reason")
    private String rejectionReason;
    
    @Schema(description = "List of project phases")
    private List<ProjectPhaseRequest> projectPhases;
    
    @Schema(description = "User ID who is submitting the proposal")
    private String userId;
}
