package com.ensar.forge.service;

import com.ensar.forge.dto.*;
import com.ensar.forge.entity.*;
import com.ensar.forge.repository.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProposalService {

    private final ProposalRepository proposalRepository;
    private final ProjectRepository projectRepository;
    private final ProjectPhaseRepository projectPhaseRepository;
    private final UserService userService;

    public ProposalResponse createProposal(ProposalRequest request) {
        // Check if user already has a proposal for this project
        User currentUser = userService.getCurrentUser();
        if (proposalRepository.existsByProjectIdAndUserId(request.getProjectId(), currentUser.getId())) {
            throw new RuntimeException("You already have a proposal for this project");
        }

        // Validate project exists
        Project project = projectRepository.findById(request.getProjectId())
                .orElseThrow(() -> new RuntimeException("Project not found with id: " + request.getProjectId()));

        // Create proposal
        Proposal proposal = new Proposal();
        proposal.setProject(project);
        proposal.setUser(currentUser);
        proposal.setStatus(request.getStatus());
        proposal.setTotalBudget(request.getTotalBudget());
        proposal.setTimelineWeeks(request.getTimelineWeeks());
        proposal.setDescription(request.getDescription());
        proposal.setDeliverables(request.getDeliverables());
        proposal.setApproachMethodology(request.getApproachMethodology());
        proposal.setRelevantExperience(request.getRelevantExperience());

        Proposal savedProposal = proposalRepository.save(proposal);

        // Add project phases if provided
        if (request.getProjectPhases() != null && !request.getProjectPhases().isEmpty()) {
            addProjectPhasesToProposal(savedProposal.getId(), request.getProjectPhases());
        }

        return mapToResponse(savedProposal);
    }

    public ProposalResponse updateProposal(String id, ProposalRequest request) {
        Proposal proposal = proposalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));

        User currentUser = userService.getCurrentUser();

        System.out.println("user ids" + proposal.getUser().getId() + currentUser.getId());

        // Check if user owns this proposal
        if (!proposal.getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You can only update your own proposals");
        }

        // Check if proposal can be updated (not approved or rejected)
        if (proposal.getStatus() == Proposal.Status.APPROVED || proposal.getStatus() == Proposal.Status.REJECTED) {
            throw new RuntimeException("Cannot update approved or rejected proposals");
        }

        // Update proposal fields
        proposal.setStatus(request.getStatus());
        proposal.setTotalBudget(request.getTotalBudget());
        proposal.setTimelineWeeks(request.getTimelineWeeks());
        proposal.setDescription(request.getDescription());
        proposal.setDeliverables(request.getDeliverables());
        proposal.setApproachMethodology(request.getApproachMethodology());
        proposal.setRelevantExperience(request.getRelevantExperience());
        proposal.setRevisionNumber(proposal.getRevisionNumber() + 1);

        Proposal updatedProposal = proposalRepository.save(proposal);

        // Update project phases if provided
        if (request.getProjectPhases() != null) {
            updateProjectPhases(updatedProposal.getId(), request.getProjectPhases());
        }

        return mapToResponse(updatedProposal);
    }

    public ProposalResponse getProposal(String id) {
        Proposal proposal = proposalRepository.findByIdWithPhasesAndAttachments(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));
        return mapToResponse(proposal);
    }

    public List<ProposalResponse> getAllProposals() {
        return proposalRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<ProposalResponse> getProposalsByProject(String projectId) {
        return proposalRepository.findByProjectIdWithPhasesAndAttachments(projectId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<ProposalResponse> getProposalsByUser(String userId) {
        return proposalRepository.findByUserIdWithPhasesAndAttachments(userId).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<ProposalResponse> getMyProposals() {
        User currentUser = userService.getCurrentUser();
        return getProposalsByUser(currentUser.getId());
    }


    public Page<ProposalResponse> getAllProposalsWithPagination(
            int page,
            int size,
            Optional<String> projectId,
            Optional<String> userId,
            Optional<String> status,
            Optional<String> minBudget,
            Optional<String> maxBudget,
            Optional<String> minTimeline,
            Optional<String> maxTimeline,
            Optional<String> searchTerm) {

        Pageable pageable = PageRequest.of(page, size);

        User currentUser = userService.getCurrentUser();

        String currentUserId = null;
        String projectOwnerId = null;

        // Set filtering based on user role
        if (currentUser != null) {
            if (currentUser.getRole() == User.Role.PROVIDING_SI) {
                // PROVIDING_SI users see only their own proposals
                currentUserId = currentUser.getId();
            } else if (currentUser.getRole() == User.Role.REQUESTING_SI) {
                // REQUESTING_SI users see proposals for their projects
                projectOwnerId = currentUser.getId();
            }
        }

        // Parse status
        Proposal.Status statusEnum = null;
        if (status.isPresent() && !status.get().isEmpty()) {
            try {
                statusEnum = Proposal.Status.valueOf(status.get().toUpperCase());
            } catch (IllegalArgumentException e) {
                // Invalid status, will be ignored
            }
        }

        // Parse budget values
        java.math.BigDecimal minBudgetValue = null;
        java.math.BigDecimal maxBudgetValue = null;
        if (minBudget.isPresent() && !minBudget.get().isEmpty()) {
            try {
                minBudgetValue = new java.math.BigDecimal(minBudget.get());
            } catch (NumberFormatException e) {
                // Invalid budget format, will be ignored
            }
        }
        if (maxBudget.isPresent() && !maxBudget.get().isEmpty()) {
            try {
                maxBudgetValue = new java.math.BigDecimal(maxBudget.get());
            } catch (NumberFormatException e) {
                // Invalid budget format, will be ignored
            }
        }

        // Parse timeline values
        Integer minTimelineValue = null;
        Integer maxTimelineValue = null;
        if (minTimeline.isPresent() && !minTimeline.get().isEmpty()) {
            try {
                minTimelineValue = Integer.parseInt(minTimeline.get());
            } catch (NumberFormatException e) {
                // Invalid timeline format, will be ignored
            }
        }
        if (maxTimeline.isPresent() && !maxTimeline.get().isEmpty()) {
            try {
                maxTimelineValue = Integer.parseInt(maxTimeline.get());
            } catch (NumberFormatException e) {
                // Invalid timeline format, will be ignored
            }
        }

        Page<Proposal> proposalsPage = proposalRepository.findAllWithFilters(
                projectId.orElse(null),
                userId.orElse(null),
                statusEnum,
                minBudgetValue,
                maxBudgetValue,
                minTimelineValue,
                maxTimelineValue,
                currentUserId,
                projectOwnerId,
                searchTerm.orElse(null),
                pageable
        );

        return proposalsPage.map(this::mapToResponse);
    }

    public List<ProposalResponse> getProposalsForMyProjects() {
        User currentUser = userService.getCurrentUser();

        // Only REQUESTING_SI users can access this endpoint
        if (currentUser.getRole() != User.Role.REQUESTING_SI) {
            throw new RuntimeException("This endpoint is only available for REQUESTING_SI users");
        }

        // Get all projects owned by the current user
        List<Project> userProjects = projectRepository.findByUserId(currentUser.getId());

        // Get all proposals for these projects
        List<Proposal> proposals = new ArrayList<>();
        for (Project project : userProjects) {
            List<Proposal> projectProposals = proposalRepository.findByProjectIdWithPhasesAndAttachments(project.getId());
            proposals.addAll(projectProposals);
        }

        return proposals.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public ProposalResponse reviewProposal(String id, Proposal.Status newStatus, String reviewNotes) {
        Proposal proposal = proposalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));

        User currentUser = userService.getCurrentUser();

        // Check if user has permission to review (admin or project owner)
        if (currentUser.getRole() != User.Role.ADMIN && 
            currentUser.getRole() != User.Role.SUPER_ADMIN &&
            !proposal.getProject().getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You don't have permission to review this proposal");
        }

        proposal.setStatus(newStatus);
        proposal.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        proposal.setReviewedBy(currentUser);

        Proposal updatedProposal = proposalRepository.save(proposal);
        return mapToResponse(updatedProposal);
    }

    public ProposalResponse acceptProposal(String id) {
        Proposal proposal = proposalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));

        User currentUser = userService.getCurrentUser();

        // Check if user has permission to accept (admin or project owner)
        if (currentUser.getRole() != User.Role.ADMIN && 
            currentUser.getRole() != User.Role.SUPER_ADMIN &&
            !proposal.getProject().getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You don't have permission to accept this proposal");
        }

        // Check if proposal can be accepted (must be UNDER_REVIEW or APPROVED)
        if (proposal.getStatus() != Proposal.Status.UNDER_REVIEW && 
            proposal.getStatus() != Proposal.Status.PENDING) {
            throw new RuntimeException("Only proposals under review or pending can be accepted");
        }

        proposal.setStatus(Proposal.Status.APPROVED);
        proposal.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        proposal.setReviewedBy(currentUser);
        proposal.setRejectionReason(null); // Clear any previous rejection reason

        // Update project status to IN_PROGRESS when proposal is accepted
        Project project = proposal.getProject();
        project.setStatus(Project.Status.IN_PROGRESS);
        projectRepository.save(project);

        Proposal updatedProposal = proposalRepository.save(proposal);
        return mapToResponse(updatedProposal);
    }

    public ProposalResponse rejectProposal(String id, String rejectionReason) {
        Proposal proposal = proposalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));

        User currentUser = userService.getCurrentUser();

        // Check if user has permission to reject (admin or project owner)
        if (currentUser.getRole() != User.Role.ADMIN && 
            currentUser.getRole() != User.Role.SUPER_ADMIN &&
            !proposal.getProject().getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You don't have permission to reject this proposal");
        }

        // Check if proposal can be rejected (must not be already rejected or in progress)
        if (proposal.getStatus() == Proposal.Status.REJECTED) {
            throw new RuntimeException("Proposal is already rejected");
        }
        if (proposal.getStatus() == Proposal.Status.IN_PROGRESS) {
            throw new RuntimeException("Cannot reject a proposal that is already in progress");
        }

        if (rejectionReason == null || rejectionReason.trim().isEmpty()) {
            throw new RuntimeException("Rejection reason is required");
        }

        proposal.setStatus(Proposal.Status.REJECTED);
        proposal.setRejectionReason(rejectionReason.trim());
        proposal.setReviewedAt(new java.sql.Timestamp(System.currentTimeMillis()));
        proposal.setReviewedBy(currentUser);

        Proposal updatedProposal = proposalRepository.save(proposal);
        return mapToResponse(updatedProposal);
    }

    public void deleteProposal(String id) {
        Proposal proposal = proposalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + id));

        User currentUser = userService.getCurrentUser();
        
        // Check if user owns this proposal
        if (!proposal.getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You can only delete your own proposals");
        }

        // Check if proposal can be deleted (not approved)
        if (proposal.getStatus() == Proposal.Status.APPROVED) {
            throw new RuntimeException("Cannot delete approved proposals");
        }

        proposalRepository.deleteById(id);
    }

    public void addProjectPhasesToProposal(String proposalId, List<ProjectPhaseRequest> phaseRequests) {
        Proposal proposal = proposalRepository.findById(proposalId)
                .orElseThrow(() -> new RuntimeException("Proposal not found with id: " + proposalId));

        for (ProjectPhaseRequest phaseRequest : phaseRequests) {
            // Check if a phase with the same title and proposal ID already exists
            ProjectPhase existingPhase = null;

            if(phaseRequest.getId() != null && !phaseRequest.getId().isEmpty()) {
                 existingPhase = projectPhaseRepository.getById(phaseRequest.getId());
            }

            System.out.println("existing sprint " + existingPhase);
            if (existingPhase != null) {
                // If exists, update the existing phase
                existingPhase.setName(phaseRequest.getName());
                existingPhase.setDurationWeeks(phaseRequest.getDurationWeeks());
                existingPhase.setCost(phaseRequest.getCost());
                projectPhaseRepository.save(existingPhase);  // Update the existing phase
            } else {
                // If not exists, create a new phase
                ProjectPhase newPhase = new ProjectPhase();
                newPhase.setProposal(proposal);
                newPhase.setName(phaseRequest.getName());
                newPhase.setDurationWeeks(phaseRequest.getDurationWeeks());
                newPhase.setCost(phaseRequest.getCost());
                projectPhaseRepository.save(newPhase);  // Save the new phase
            }
        }
    }

    public void updateProjectPhases(String proposalId, List<ProjectPhaseRequest> phaseRequests) {
        // Remove all existing phases
        List<ProjectPhase> existingPhases = projectPhaseRepository.findByProposalId(proposalId);
//        projectPhaseRepository.deleteAll(existingPhases);

        // Add new phases
        if (phaseRequests != null && !phaseRequests.isEmpty()) {
            addProjectPhasesToProposal(proposalId, phaseRequests);
        }
    }

//    public void updateProjectPhases(String proposalId, List<ProjectPhaseRequest> phaseRequests) {
//        // Process each phase from the request
//        for (ProjectPhaseRequest phaseRequest : phaseRequests) {
//            String phaseName = phaseRequest.getName();
//            boolean phaseExists = projectPhaseRepository.existsByProposalIdAndName(proposalId, phaseName);
//
//            if (!phaseExists) {
//                addProjectPhasesToProposal(proposalId, phaseRequests);
//            }
//        }
//    }

    public List<ProjectPhaseResponse> getProjectPhases(String proposalId) {
        return projectPhaseRepository.findByProposalIdOrderByCreatedDateTime(proposalId).stream()
                .map(this::mapProjectPhaseToResponse)
                .collect(Collectors.toList());
    }

    private ProposalResponse mapToResponse(Proposal proposal) {
        ProposalResponse response = new ProposalResponse();
        response.setId(proposal.getId());
        response.setStatus(proposal.getStatus());
        response.setTotalBudget(proposal.getTotalBudget());
        response.setTimelineWeeks(proposal.getTimelineWeeks());
        response.setDescription(proposal.getDescription());
        response.setDeliverables(proposal.getDeliverables());
        response.setApproachMethodology(proposal.getApproachMethodology());
        response.setRelevantExperience(proposal.getRelevantExperience());
        response.setRejectionReason(proposal.getRejectionReason());
        response.setReviewedAt(proposal.getReviewedAt());
        response.setRevisionNumber(proposal.getRevisionNumber());
        response.setCreatedDateTime(proposal.getCreatedDateTime());
        response.setLastUpdatedDateTime(proposal.getLastUpdatedDateTime());

        // Map project information
        if (proposal.getProject() != null) {
            ProjectResponse projectResponse = new ProjectResponse();
            projectResponse.setId(proposal.getProject().getId());
            projectResponse.setTitle(proposal.getProject().getTitle());
            projectResponse.setDescription(proposal.getProject().getDescription());
            projectResponse.setMinBudget(proposal.getProject().getMinBudget());
            projectResponse.setMaxBudget(proposal.getProject().getMaxBudget());
            projectResponse.setLocation(proposal.getProject().getLocation());
            projectResponse.setDeadline(proposal.getProject().getDeadline());
            projectResponse.setPriority(proposal.getProject().getPriority());
            projectResponse.setCreatedDateTime(proposal.getProject().getCreatedDateTime());
            projectResponse.setLastUpdatedDateTime(proposal.getProject().getLastUpdatedDateTime());
            response.setProject(projectResponse);
        }

        // Map user information
        if (proposal.getUser() != null) {
            UserResponse userResponse = new UserResponse();
            userResponse.setId(proposal.getUser().getId());
            userResponse.setFirstName(proposal.getUser().getFirstName());
            userResponse.setLastName(proposal.getUser().getLastName());
            userResponse.setEmail(proposal.getUser().getEmail());
            userResponse.setRole(proposal.getUser().getRole());
            userResponse.setEnabled(proposal.getUser().getEnabled());
            userResponse.setCreatedDateTime(proposal.getUser().getCreatedDateTime());
            userResponse.setLastUpdatedDateTime(proposal.getUser().getLastUpdatedDateTime());
            response.setUser(userResponse);
        }

        // Map reviewed by information
        if (proposal.getReviewedBy() != null) {
            UserResponse reviewedByResponse = new UserResponse();
            reviewedByResponse.setId(proposal.getReviewedBy().getId());
            reviewedByResponse.setFirstName(proposal.getReviewedBy().getFirstName());
            reviewedByResponse.setLastName(proposal.getReviewedBy().getLastName());
            reviewedByResponse.setEmail(proposal.getReviewedBy().getEmail());
            reviewedByResponse.setRole(proposal.getReviewedBy().getRole());
            reviewedByResponse.setEnabled(proposal.getReviewedBy().getEnabled());
            reviewedByResponse.setCreatedDateTime(proposal.getReviewedBy().getCreatedDateTime());
            reviewedByResponse.setLastUpdatedDateTime(proposal.getReviewedBy().getLastUpdatedDateTime());
            response.setReviewedBy(reviewedByResponse);
        }

        // Map project phases
        List<ProjectPhaseResponse> phases = proposal.getProjectPhases() != null
                ? proposal.getProjectPhases().stream()
                .map(this::mapProjectPhaseToResponse)
                .collect(Collectors.toList())
                : Collections.emptyList();
        response.setProjectPhases(phases);

        // Map attachments
        List<ProposalAttachmentResponse> attachments = proposal.getAttachments() != null
                ? proposal.getAttachments().stream()
                .map(this::mapAttachmentToResponse)
                .collect(Collectors.toList())
                : Collections.emptyList();
        response.setAttachments(attachments);

        return response;
    }

    private ProjectPhaseResponse mapProjectPhaseToResponse(ProjectPhase phase) {
        ProjectPhaseResponse response = new ProjectPhaseResponse();
        response.setId(phase.getId());
        response.setName(phase.getName());
        response.setDurationWeeks(phase.getDurationWeeks());
        response.setCost(phase.getCost());
        response.setCreatedDateTime(phase.getCreatedDateTime());
        response.setLastUpdatedDateTime(phase.getLastUpdatedDateTime());
        return response;
    }

    private ProposalAttachmentResponse mapAttachmentToResponse(ProposalAttachment attachment) {
        ProposalAttachmentResponse response = new ProposalAttachmentResponse();
        response.setId(attachment.getId());
        response.setFileName(attachment.getFileName());
        response.setFileType(attachment.getFileType());
        response.setFileSize((long) attachment.getData().length);
        response.setCreatedDateTime(attachment.getCreatedDateTime());
        response.setLastUpdatedDateTime(attachment.getLastUpdatedDateTime());
        return response;
    }
}
