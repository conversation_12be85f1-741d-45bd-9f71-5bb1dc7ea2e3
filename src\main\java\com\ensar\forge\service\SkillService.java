package com.ensar.forge.service;

import com.ensar.forge.dto.SkillRequest;
import com.ensar.forge.dto.SkillResponse;
import com.ensar.forge.entity.Skill;
import com.ensar.forge.repository.SkillRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class SkillService {

    private final SkillRepository skillRepository;

    public SkillResponse createSkill(SkillRequest request) {
        if (skillRepository.existsByNameIgnoreCase(request.getName())) {
            throw new RuntimeException("Skill with name '" + request.getName() + "' already exists");
        }

        Skill skill = new Skill();
        skill.setName(request.getName());
        skill.setDescription(request.getDescription());

        Skill savedSkill = skillRepository.save(skill);
        return mapToResponse(savedSkill);
    }

    public SkillResponse updateSkill(String id, SkillRequest request) {
        Skill skill = skillRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Skill not found with id: " + id));

        if (!skill.getName().equals(request.getName()) && skillRepository.existsByNameIgnoreCase(request.getName())) {
            throw new RuntimeException("Skill with name '" + request.getName() + "' already exists");
        }

        skill.setName(request.getName());
        skill.setDescription(request.getDescription());

        Skill updatedSkill = skillRepository.save(skill);
        return mapToResponse(updatedSkill);
    }

    public SkillResponse getSkill(String id) {
        Skill skill = skillRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Skill not found with id: " + id));
        return mapToResponse(skill);
    }

    public List<SkillResponse> getAllSkills() {
        return skillRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public List<SkillResponse> searchSkillsByName(String name) {
        return skillRepository.findByNameContainingIgnoreCase(name).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void deleteSkill(String id) {
        if (!skillRepository.existsById(id)) {
            throw new RuntimeException("Skill not found with id: " + id);
        }
        skillRepository.deleteById(id);
    }

    private SkillResponse mapToResponse(Skill skill) {
        SkillResponse response = new SkillResponse();
        response.setId(skill.getId());
        response.setName(skill.getName());
        response.setDescription(skill.getDescription());
        response.setCreatedDateTime(skill.getCreatedDateTime());
        response.setLastUpdatedDateTime(skill.getLastUpdatedDateTime());
        return response;
    }
}




