package com.ensar.forge.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "companies")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Company extends BaseEntity {

    @Column(name = "name", nullable = false, length = 50, unique = true)
    private String name;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "domain", length = 50)
    private String domain;

    @Column(name = "disabled", nullable = false)
    private Boolean disabled = false;
}