package com.ensar.forge.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Data
public class CompanyRequest {
    
    @NotBlank(message = "Company name is required")
    @Size(max = 50, message = "Company name cannot exceed 50 characters")
    private String name;
    
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;
    
    @Size(max = 50, message = "Domain cannot exceed 50 characters")
    private String domain;
    
    private Boolean disabled = false;
}


