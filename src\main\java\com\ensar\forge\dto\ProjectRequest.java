package com.ensar.forge.dto;

import com.ensar.forge.entity.Project;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import com.ensar.forge.entity.Project.Priority;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

@Data
@Schema(description = "Request object for creating or updating a project")
public class ProjectRequest {
    
    @Schema(description = "Project title", example = "Web Application Development", required = true)
    @NotBlank(message = "Project title is required")
    @Size(max = 255, message = "Project title cannot exceed 255 characters")
    private String title;
    
    @Schema(description = "Project description", example = "A modern web application using React and Spring Boot")
    private String description;

    @Schema(description = "Project status", defaultValue = "ACTIVE")
    private Project.Status status = Project.Status.ACTIVE;

    @Schema(description = "Minimum budget for the project", example = "20000.00")
    @NotNull(message = "Minimum budget is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Minimum budget must be greater than 0")
    private BigDecimal minBudget;

    @Schema(description = "Maximum budget for the project", example = "30000.00")
    @NotNull(message = "Maximum budget is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Maximum budget must be greater than 0")
    private BigDecimal maxBudget;
    
    @Schema(description = "Project location", example = "Remote")
    @Size(max = 100, message = "Location cannot exceed 100 characters")
    private String location;
    
    @Schema(description = "Project deadline", example = "2024-06-30")
    private Date deadline;
    
    @Schema(description = "Project priority level", example = "HIGH", defaultValue = "MEDIUM")
    private Priority priority = Priority.MEDIUM;
    
    @Schema(description = "List of skill IDs required for this project")
    private List<String> skillsIds;
    
    @Schema(description = "User ID who created/owns this project")
    private String userId;
}
