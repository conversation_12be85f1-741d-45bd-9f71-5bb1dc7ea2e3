package com.ensar.forge;

import com.ensar.forge.controller.ProjectController;
import com.ensar.forge.dto.ProjectRequest;
import com.ensar.forge.entity.Project;
import com.ensar.forge.exception.ResourceNotFoundException;
import com.ensar.forge.exception.ResourceAlreadyExistsException;
import com.ensar.forge.service.ProjectService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class ErrorHandlingTest {

    @Autowired
    private ProjectController projectController;

    @Autowired
    private ProjectService projectService;

    @Test
    void testResourceNotFoundException() {
        // Test getting a non-existent project
        assertThrows(ResourceNotFoundException.class, () -> {
            projectService.getProject("non-existent-id");
        });
    }

    @Test
    void testResourceAlreadyExistsException() {
        // Create a project request
        ProjectRequest request = new ProjectRequest();
        request.setTitle("Test Project");
        request.setDescription("Test Description");
        request.setStatus(Project.Status.ACTIVE);
        request.setMinBudget(new BigDecimal("1000"));
        request.setMaxBudget(new BigDecimal("5000"));
        request.setLocation("Test Location");
        request.setDeadline(Date.valueOf(LocalDate.now().plusDays(30)));
        request.setPriority(Project.Priority.MEDIUM);

        // First creation should succeed
        ResponseEntity<?> response1 = projectController.createProject(request);
        assertEquals(HttpStatus.CREATED, response1.getStatusCode());

        // Second creation with same title should fail
        assertThrows(ResourceAlreadyExistsException.class, () -> {
            projectController.createProject(request);
        });
    }

    @Test
    void testValidationException() {
        // Test with invalid data that should trigger validation
        ProjectRequest request = new ProjectRequest();
        // Missing required fields to trigger validation errors
        
        // This should be handled by the global exception handler
        ResponseEntity<?> response = projectController.createProject(request);
        
        // The response should be handled by the global exception handler
        // and return a proper error response instead of throwing an exception
        assertNotNull(response);
    }
}
