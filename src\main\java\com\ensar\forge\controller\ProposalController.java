package com.ensar.forge.controller;

import com.ensar.forge.dto.ProposalRequest;
import com.ensar.forge.dto.ProposalResponse;
import com.ensar.forge.entity.Proposal.Status;
import com.ensar.forge.service.ProposalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/proposals")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "Proposal Management", description = "APIs for managing project proposals")
public class ProposalController {

    private final ProposalService proposalService;

    @PostMapping
    @Operation(
        summary = "Create a new proposal",
        description = "Creates a new proposal for a project with the provided details"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Proposal created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "Proposal already exists for this project")
    })
    public ResponseEntity<ProposalResponse> createProposal(
            @Parameter(description = "Proposal details", required = true)
            @Valid @RequestBody ProposalRequest request) {
        ProposalResponse response = proposalService.createProposal(request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(
        summary = "Update an existing proposal",
        description = "Updates a proposal with the provided details. Only the proposal owner can update it."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Proposal updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update this proposal"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<ProposalResponse> updateProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id, 
            @Parameter(description = "Updated proposal details", required = true)
            @Valid @RequestBody ProposalRequest request) {
        ProposalResponse response = proposalService.updateProposal(id, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    @Operation(
        summary = "Get proposal by ID",
        description = "Retrieves a specific proposal with all its details, phases, and attachments"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Proposal retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<ProposalResponse> getProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProposalResponse response = proposalService.getProposal(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(
        summary = "Get all proposals with pagination and filters",
        description = "Retrieves a paginated list of proposals with optional filtering"
    )
    @ApiResponse(responseCode = "200", description = "Proposals retrieved successfully")
    public ResponseEntity<Page<ProposalResponse>> getAllProposals(
            @Parameter(description = "Page number", required = false)
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", required = false)
            @RequestParam(defaultValue = "25") int size,
            @Parameter(description = "Project ID filter", required = false)
            @RequestParam Optional<String> projectId,
            @Parameter(description = "User ID filter", required = false)
            @RequestParam Optional<String> userId,
            @Parameter(description = "Status filter", required = false, schema = @Schema(allowableValues = {"DRAFT", "UNDER_REVIEW", "APPROVED", "REJECTED", "PENDING", "IN_PROGRESS"}))
            @RequestParam Optional<String> status,
            @Parameter(description = "Minimum budget filter", required = false)
            @RequestParam Optional<String> minBudget,
            @Parameter(description = "Maximum budget filter", required = false)
            @RequestParam Optional<String> maxBudget,
            @Parameter(description = "Minimum timeline filter (weeks)", required = false)
            @RequestParam Optional<String> minTimeline,
            @Parameter(description = "Maximum timeline filter (weeks)", required = false)
            @RequestParam Optional<String> maxTimeline,
            @Parameter(description = "Search term for project title", required = false)
            @RequestParam Optional<String> searchTerm) {
        Page<ProposalResponse> proposals = proposalService.getAllProposalsWithPagination(
                page, size, projectId, userId, status, minBudget, maxBudget, minTimeline, maxTimeline,searchTerm);
        return ResponseEntity.ok(proposals);
    }

    @GetMapping("/all")
    @Operation(
        summary = "Get all proposals",
        description = "Retrieves a list of all proposals with their basic information"
    )
    public ResponseEntity<List<ProposalResponse>> getAllProposals() {
        List<ProposalResponse> proposals = proposalService.getAllProposals();
        return ResponseEntity.ok(proposals);
    }

    @GetMapping("/my-project-proposals")
    @Operation(
            summary = "Get proposals for my projects",
            description = "Retrieves all proposals submitted for projects owned by the current REQUESTING_SI user"
    )
    @ApiResponse(responseCode = "200", description = "Proposals retrieved successfully")
    @ApiResponse(responseCode = "403", description = "Access denied - only REQUESTING_SI users can access this endpoint")
    public ResponseEntity<List<ProposalResponse>> getProposalsForMyProjects() {
        List<ProposalResponse> proposals = proposalService.getProposalsForMyProjects();
        return ResponseEntity.ok(proposals);
    }


    @GetMapping("/project/{projectId}")
    @Operation(
        summary = "Get proposals by project",
        description = "Retrieves all proposals for a specific project"
    )
    public ResponseEntity<List<ProposalResponse>> getProposalsByProject(
            @Parameter(description = "Project ID", required = true, example = "uuid-here")
            @PathVariable String projectId) {
        List<ProposalResponse> proposals = proposalService.getProposalsByProject(projectId);
        return ResponseEntity.ok(proposals);
    }

    @GetMapping("/user/{userId}")
    @Operation(
        summary = "Get proposals by user",
        description = "Retrieves all proposals submitted by a specific user"
    )
    public ResponseEntity<List<ProposalResponse>> getProposalsByUser(
            @Parameter(description = "User ID", required = true, example = "uuid-here")
            @PathVariable String userId) {
        List<ProposalResponse> proposals = proposalService.getProposalsByUser(userId);
        return ResponseEntity.ok(proposals);
    }

    @GetMapping("/my-proposals")
    @Operation(
        summary = "Get my proposals",
        description = "Retrieves all proposals submitted by the current user"
    )
    public ResponseEntity<List<ProposalResponse>> getMyProposals() {
        List<ProposalResponse> proposals = proposalService.getMyProposals();
        return ResponseEntity.ok(proposals);
    }

    @PutMapping("/{id}/review")
    @Operation(
        summary = "Review a proposal",
        description = "Updates the status of a proposal (approve/reject). Only admins or project owners can review proposals."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Proposal reviewed successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to review this proposal"),
        @ApiResponse(responseCode = "404", description = "Proposal not found")
    })
    public ResponseEntity<ProposalResponse> reviewProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id,
            @Parameter(description = "New status for the proposal", required = true, schema = @Schema(allowableValues = {"APPROVED", "REJECTED", "IN_PROGRESS"}))
            @RequestParam Status newStatus,
            @Parameter(description = "Review notes", required = false)
            @RequestParam(required = false) String reviewNotes) {
        ProposalResponse response = proposalService.reviewProposal(id, newStatus, reviewNotes);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/accept")
    @Operation(
        summary = "Accept a proposal",
        description = "Accepts a proposal, changes its status to APPROVED, and updates the associated project status to IN_PROGRESS. Only admins or project owners can accept proposals."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Proposal accepted successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to accept this proposal"),
        @ApiResponse(responseCode = "404", description = "Proposal not found"),
        @ApiResponse(responseCode = "400", description = "Proposal cannot be accepted in its current status")
    })
    public ResponseEntity<ProposalResponse> acceptProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        ProposalResponse response = proposalService.acceptProposal(id);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/reject")
    @Operation(
        summary = "Reject a proposal",
        description = "Rejects a proposal with a reason and changes its status to REJECTED. Only admins or project owners can reject proposals."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Proposal rejected successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to reject this proposal"),
        @ApiResponse(responseCode = "404", description = "Proposal not found"),
        @ApiResponse(responseCode = "400", description = "Proposal cannot be rejected or rejection reason is missing")
    })
    public ResponseEntity<ProposalResponse> rejectProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id,
            @Parameter(description = "Reason for rejecting the proposal", required = true, example = "Budget exceeds project requirements")
            @RequestParam String rejectionReason) {
        ProposalResponse response = proposalService.rejectProposal(id, rejectionReason);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @Operation(
        summary = "Delete a proposal",
        description = "Permanently deletes a proposal. Only the proposal owner can delete it, and approved proposals cannot be deleted."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Proposal deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete this proposal"),
        @ApiResponse(responseCode = "404", description = "Proposal not found"),
        @ApiResponse(responseCode = "400", description = "Cannot delete approved proposals")
    })
    public ResponseEntity<Void> deleteProposal(
            @Parameter(description = "Proposal ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        proposalService.deleteProposal(id);
        return ResponseEntity.noContent().build();
    }
}
