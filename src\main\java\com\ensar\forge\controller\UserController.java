package com.ensar.forge.controller;

import com.ensar.forge.dto.ProjectResponse;
import com.ensar.forge.dto.UserRegistrationRequest;
import com.ensar.forge.dto.UserResponse;
import com.ensar.forge.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Tag(name = "User Management", description = "APIs for managing users")
public class UserController {

    private final UserService userService;

    @GetMapping
    @Operation(
        summary = "Get all users",
        description = "Retrieves a list of all registered users"
    )
    @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    public ResponseEntity<List<UserResponse>> getAllUsers() {
        List<UserResponse> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        return ResponseEntity.ok(userService.getCurrentUser());
    }

    @GetMapping("/{id}")
    @Operation(
            summary = "Get User by ID",
            description = "Retrieves a specific User with all its details and associated skills"
    )
    public ResponseEntity<UserResponse> getUser(
            @Parameter(description = "User ID", required = true, example = "uuid-here")
            @PathVariable String id) {
        UserResponse response = userService.getUserById(id);
        return ResponseEntity.ok(response);
    }

//    @DeleteMapping("/{id}")
//    @Operation(
//        summary = "Delete a user",
//        description = "Permanently deletes a user account"
//    )
//    @ApiResponses(value = {
//        @ApiResponse(responseCode = "204", description = "User deleted successfully"),
//        @ApiResponse(responseCode = "404", description = "User not found")
//    })
//    public ResponseEntity<Void> deleteUser(
//            @Parameter(description = "User ID", required = true, example = "uuid-here")
//            @PathVariable String id) {
//        userService.deleteUser(id);
//        return ResponseEntity.noContent().build();
//    }
}
