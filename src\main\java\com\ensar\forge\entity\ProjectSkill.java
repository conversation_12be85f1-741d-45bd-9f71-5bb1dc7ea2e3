package com.ensar.forge.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "project_skills", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"project_id", "skill_id"}))
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProjectSkill extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @ManyToOne
    @JoinColumn(name = "skill_id", nullable = false)
    private Skill skill;
}


