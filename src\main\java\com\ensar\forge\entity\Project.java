package com.ensar.forge.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

@Entity
@Table(name = "projects")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = {"projectSkills"})
@SQLDelete(sql = "UPDATE projects SET is_deleted = true WHERE id = ?")
@Where(clause = "is_deleted = false")
public class Project extends BaseEntity {

    @Column(name = "title", nullable = false, length = 255)
    private String title;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "min_budget")
    private BigDecimal minBudget;

    @Column(name = "max_budget")
    private BigDecimal maxBudget;

    @Column(name = "location", length = 100)
    private String location;

    @Column(name = "deadline")
    private Date deadline;

    @Column(name = "is_deleted")
    private boolean isDeleted=false;


    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false)
    private Priority priority = Priority.MEDIUM;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.ACTIVE;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ProjectSkill> projectSkills = new LinkedHashSet<>();

    public enum Priority {
        LOW, MEDIUM, HIGH
    }

    public enum Status {
        ACTIVE,IN_PROGRESS, PENDING, COMPLETED
    }

    // Helper methods for managing project skills
    public void addProjectSkill(ProjectSkill projectSkill) {
        projectSkills.add(projectSkill);
        projectSkill.setProject(this);
    }

    public void removeProjectSkill(ProjectSkill projectSkill) {
        projectSkills.remove(projectSkill);
        projectSkill.setProject(null);
    }
}
