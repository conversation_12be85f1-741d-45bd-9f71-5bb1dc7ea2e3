package com.ensar.forge.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

@Entity
@Table(name = "proposals")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, exclude = {"projectPhases", "attachments"})
@SQLDelete(sql = "UPDATE proposals SET is_deleted = true WHERE id = ?")
@Where(clause = "is_deleted = false")
public class Proposal extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.DRAFT;

    @Column(name = "total_budget", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalBudget;

    @Column(name = "timeline_weeks", nullable = false)
    private Integer timelineWeeks;

    @Column(name = "description", columnDefinition = "TEXT", nullable = false)
    private String description;

    @Column(name = "deliverables", columnDefinition = "JSON")
    private String deliverables; // JSON string

    @Column(name = "approach_methodology", columnDefinition = "TEXT")
    private String approachMethodology;

    @Column(name = "relevant_experience", columnDefinition = "TEXT")
    private String relevantExperience;

    @Column(name = "rejection_reason")
    private String rejectionReason;

    @Column(name = "reviewed_at")
    private Timestamp reviewedAt;

    @ManyToOne
    @JoinColumn(name = "reviewed_by")
    private User reviewedBy;

    @Column(name = "revision_number")
    private Integer revisionNumber = 1;

    @Column(name = "is_deleted")
    private boolean isDeleted = false;

    @OneToMany(mappedBy = "proposal", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ProjectPhase> projectPhases = new LinkedHashSet<>();

    @OneToMany(mappedBy = "proposal", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ProposalAttachment> attachments = new LinkedHashSet<>();

    public enum Status {
        DRAFT, UNDER_REVIEW, APPROVED, REJECTED, PENDING, IN_PROGRESS
    }

    // Helper methods for managing project phases
    public void addProjectPhase(ProjectPhase projectPhase) {
        projectPhases.add(projectPhase);
        projectPhase.setProposal(this);
    }

    public void removeProjectPhase(ProjectPhase projectPhase) {
        projectPhases.remove(projectPhase);
        projectPhase.setProposal(null);
    }

    // Helper methods for managing attachments
    public void addAttachment(ProposalAttachment attachment) {
        attachments.add(attachment);
        attachment.setProposal(this);
    }

    public void removeAttachment(ProposalAttachment attachment) {
        attachments.remove(attachment);
        attachment.setProposal(null);
    }
}
