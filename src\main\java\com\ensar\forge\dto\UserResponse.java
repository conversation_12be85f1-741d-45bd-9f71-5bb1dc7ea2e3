package com.ensar.forge.dto;

import com.ensar.forge.entity.User;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class UserResponse {
    private String id;
    private String firstName;
    private String lastName;
    private String email;
    private User.Role role;
    private LocalDate accountExpiryDate;
    private Boolean accountNonExpired;
    private Boolean accountNonLocked;
    private LocalDate credentialsExpiryDate;
    private Boolean credentialsNonExpired;
    private Boolean enabled;
    private Boolean isTwoFactorEnabled;
    private String signUpMethod;
    private String twoFactorSecret;
    private UserCompanyResponse company;
    private List<UserSkillResponse> skills;
    private java.sql.Timestamp createdDateTime;
    private java.sql.Timestamp lastUpdatedDateTime;
}




